(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"447x559",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx","default")},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,s;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,s]=e.split(r,2);break}if(!t||!r||!s)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":s="/"===t?"/"+s:t+"/"+s;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:s}}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return m},normalizeMetadataPageToRoute:function(){return f},normalizeMetadataRoute:function(){return p}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),s=r(6341),a=r(4396),o=r(660),l=r(4722),d=r(2958),c=r(5499);function u(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function m(e,t,r){let n=(0,l.normalizeAppPath)(e),o=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,s.interpolateDynamicPath)(n,t,o),{name:m,ext:p}=i.default.parse(r),f=u(i.default.posix.join(e,m)),h=f?`-${f}`:"";return(0,d.normalizePathSep)(i.default.join(c,`${m}${h}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=u(e),!t.endsWith("/route")){let{dir:e,name:n,ext:s}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${s}`,"route")}return t}function f(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},1759:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=r(5239),i=r(8088),s=r(8170),a=r.n(s),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2168:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=s(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),s=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:a,searchParams:o,search:l,hash:d,href:c,origin:u}=new URL(e,s);if(u!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:d,href:c.slice(u.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return l}});let n=r(6143),i=r(1437),s=r(3293),a=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?d(t[2]):d(e)}function d(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let u of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>u.startsWith(e)),a=u.match(o);if(e&&a&&a[2]){let{key:t,optional:r,repeat:i}=d(a[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,s.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:i}=d(a[2]);n[e]={pos:l++,repeat:t,optional:i},r&&a[1]&&c.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,s.escapeStringRegexp)(u));t&&a&&a[3]&&c.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:n}}function u(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=c(e,r,n),o=s;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function m(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:u,repeat:m}=d(i),p=c.replace(/\W/g,"");o&&(p=""+o+p);let f=!1;(0===p.length||p.length>30)&&(f=!0),isNaN(parseInt(p.slice(0,1)))||(f=!0),f&&(p=n());let h=p in a;o?a[p]=""+o+c:a[p]=c;let g=r?(0,s.escapeStringRegexp)(r):"";return t=h&&l?"\\k<"+p+">":m?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",u?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,r,l,d){let c,u=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},f=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(o);if(e&&a&&a[2])f.push(m({getSafeRouteKey:u,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:d}));else if(a&&a[2]){l&&a[1]&&f.push("/"+(0,s.escapeStringRegexp)(a[1]));let e=m({getSafeRouteKey:u,segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:d});l&&a[1]&&(e=e.substring(1)),f.push(e)}else f.push("/"+(0,s.escapeStringRegexp)(c));r&&a&&a[3]&&f.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:f.join(""),routeKeys:p}}function f(e,t){var r,n,i;let s=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...u(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function h(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var n=r(7413),i=r(2376),s=r.n(i),a=r(8726),o=r.n(a);r(1135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:e})})}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let n=r(5531),i=r(5499);function s(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4819:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return m},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return d},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&d(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let m="undefined"!=typeof performance,p=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,s=void 0===n?"./":n,a="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,d=0,c="",u=function(e){if(d<r.length&&r[d].type===e)return r[d++].value},m=function(e){var t=u(e);if(void 0!==t)return t;var n=r[d];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};d<r.length;){var f=u("CHAR"),h=u("NAME"),g=u("PATTERN");if(h||g){var x=f||"";-1===s.indexOf(x)&&(c+=x,x=""),c&&(o.push(c),c=""),o.push({name:h||l++,prefix:x,suffix:"",pattern:g||a,modifier:u("MODIFIER")||""});continue}var y=f||u("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(o.push(c),c=""),u("OPEN")){var x=p(),b=u("NAME")||"",v=u("PATTERN")||"",j=p();m("CLOSE"),o.push({name:b||(v?l++:""),pattern:b&&!v?a:v,prefix:x,suffix:j,modifier:u("MODIFIER")||""});continue}m("END")}return o}function r(e,t){void 0===t&&(t={});var r=s(t),n=t.encode,i=void 0===n?function(e){return e}:n,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var s=e[n];if("string"==typeof s){r+=s;continue}var a=t?t[s.name]:void 0,d="?"===s.modifier||"*"===s.modifier,c="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(d)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var u=0;u<a.length;u++){var m=i(a[u],s);if(o&&!l[n].test(m))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+m+'"');r+=s.prefix+m+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var m=i(String(a),s);if(o&&!l[n].test(m))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+m+'"');r+=s.prefix+m+s.suffix;continue}if(!d){var p=c?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var s=n[0],a=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):o[r.name]=i(n[e],r)}}(l);return{path:s,index:a,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,o=r.start,l=r.end,d=r.encode,c=void 0===d?function(e){return e}:d,u="["+i(r.endsWith||"")+"]|$",m="["+i(r.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",f=0;f<e.length;f++){var h=e[f];if("string"==typeof h)p+=i(c(h));else{var g=i(c(h.prefix)),x=i(c(h.suffix));if(h.pattern)if(t&&t.push(h),g||x)if("+"===h.modifier||"*"===h.modifier){var y="*"===h.modifier?"?":"";p+="(?:"+g+"((?:"+h.pattern+")(?:"+x+g+"(?:"+h.pattern+"))*)"+x+")"+y}else p+="(?:"+g+"("+h.pattern+")"+x+")"+h.modifier;else p+="("+h.pattern+")"+h.modifier;else p+="(?:"+g+x+")"+h.modifier}}if(void 0===l||l)a||(p+=m+"?"),p+=r.endsWith?"(?="+u+")":"$";else{var b=e[e.length-1],v="string"==typeof b?m.indexOf(b[b.length-1])>-1:void 0===b;a||(p+="(?:"+m+"(?="+u+"))?"),v||(p+="(?="+m+"|"+u+")")}return new RegExp(p,s(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",s(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return d},parseDestination:function(){return u},prepareDestination:function(){return m}});let n=r(5362),i=r(3293),s=r(6759),a=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function d(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},s=r=>{let n,s=r.key;switch(r.type){case"header":s=s.toLowerCase(),n=e.headers[s];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(s)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>s(e))||n.some(e=>s(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function u(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,s.parseUrl)(t),n=r.pathname;n&&(n=l(n));let a=r.href;a&&(a=l(a));let o=r.hostname;o&&(o=l(o));let d=r.hash;return d&&(d=l(d)),{...r,pathname:n,hostname:o,href:a,hash:d}}function m(e){let t,r,i=Object.assign({},e.query),s=u(e),{hostname:o,query:d}=s,m=s.pathname;s.hash&&(m=""+m+s.hash);let p=[],f=[];for(let e of((0,n.pathToRegexp)(m,f),f))p.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))p.push(t.name)}let h=(0,n.compile)(m,{validate:!1});for(let[r,i]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(d)))Array.isArray(i)?d[r]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(d[r]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in d||(d[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=h(e.params)).split("#",2);t&&(s.hostname=t(e.params)),s.pathname=n,s.hash=(i?"#":"")+(i||""),delete s.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return s.query={...i,...s.query},{newUrl:r,destQuery:d,parsedDestination:s}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return f},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return p}});let n=r(9551),i=r(1959),s=r(2437),a=r(4396),o=r(8034),l=r(5526),d=r(2887),c=r(4722),u=r(6143),m=r(7912);function p(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX),s=e!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(n||s||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function f(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:s,repeat:a}=r.groups[n],o=`[${a?"...":""}${n}]`;s&&(o=`[${o}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,i)}return e}function h(e,t,r,n){let i={};for(let s of Object.keys(t.groups)){let a=e[s];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let o=r[s],l=t.groups[s].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete e[s]),a&&"string"==typeof a&&t.groups[s].repeat&&(a=a.split("/")),a&&(i[s]=a)}return{params:i,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:u,caseSensitive:g}){let x,y,b;return c&&(x=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(y=(0,o.getRouteMatcher)(x))(e)),{handleRewrites:function(a,o){let m={},p=o.pathname,f=n=>{let d=(0,s.getPathMatch)(n.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let f=d(o.pathname);if((n.has||n.missing)&&f){let e=(0,l.matchHas)(a,o.query,n.has,n.missing);e?Object.assign(f,e):f=!1}if(f){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:f,query:o.query});if(s.protocol)return!0;if(Object.assign(m,a,f),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(p=o.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||f.nextInternalLocale}if(p===e)return!0;if(c&&y){let e=y(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])f(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=f(e))break;if(!t&&!(()=>{let t=(0,d.removeTrailingSlash)(p||"");return t===(0,d.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=f(e))break}}return m},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,m.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let s=r[e];if(!s)continue;let a=t[s],o=n[e];if(!a.optional&&!o)return null;i[a.pos]=o}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>x&&b?h(e,x,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,x),interpolateDynamicPath:(e,t)=>f(e,t,x)}}function x(e,t){return"string"==typeof e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},s=t.split(n),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],d=l.indexOf("=");if(!(d<0)){var c=l.substr(0,d).trim(),u=l.substr(++d,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(u,a))}}return i},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var d=s.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(d)}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6459:()=>{},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let n=r(2785),i=r(3736);function s(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>s(e)):a[e]=s(r))}return a}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return u},isMetadataRoute:function(){return m},isMetadataRouteFile:function(){return d},isStaticMetadataRoute:function(){return c}});let n=r(2958),i=r(4722),s=r(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function d(e,t,r){let i=(r?"":"?")+"$",s=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${a.icon.filename}${s}${l(a.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${a.apple.filename}${s}${l(a.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${a.openGraph.filename}${s}${l(a.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${a.twitter.filename}${s}${l(a.twitter.extensions,t)}${i}`)],d=(0,n.normalizePathSep)(e);return o.some(e=>e.test(d))}function c(e){let t=e.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(e)&&d(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function u(e){return!(0,s.isAppRouteRoute)(e)&&d(e,[],!1)}function m(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,s.isAppRouteRoute)(e)&&d(t,[],!1)}},8659:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>z});var n=r(687),i=r(3210);let s=[{id:"prayer",name:"Prayer",icon:"\uD83D\uDE4F",description:"Daily spiritual practice and reflection",detailedInfo:{purpose:"Connect with the divine, find inner peace, and seek guidance for daily life.",benefits:["Reduces stress and anxiety","Provides spiritual guidance and clarity","Strengthens faith and spiritual connection","Promotes gratitude and mindfulness","Offers comfort during difficult times"],tips:["Set aside a quiet, dedicated space for prayer","Choose a consistent time each day","Start with gratitude and thanksgiving","Include prayers for others, not just yourself","Listen for guidance and inner peace"],timeRecommendation:"10-30 minutes",frequency:"Daily"}},{id:"study",name:"Study",icon:"\uD83D\uDCDA",description:"Learning, reading, or skill development",detailedInfo:{purpose:"Expand knowledge, develop skills, and pursue personal or professional growth through dedicated learning.",benefits:["Improves cognitive function and memory","Enhances career prospects and opportunities","Builds confidence and self-esteem","Keeps mind sharp and engaged","Opens new perspectives and ideas"],tips:["Create a distraction-free study environment","Use active learning techniques (notes, summaries)","Take regular breaks to maintain focus","Set specific, achievable learning goals","Review and practice regularly for retention"],timeRecommendation:"30-120 minutes",frequency:"Daily"}},{id:"hygiene",name:"Hygiene",icon:"\uD83E\uDDFC",description:"Personal care and cleanliness",detailedInfo:{purpose:"Maintain personal cleanliness, health, and confidence through proper hygiene practices.",benefits:["Prevents illness and infections","Boosts self-confidence and social acceptance","Improves overall health and well-being","Creates positive first impressions","Reduces stress and anxiety about appearance"],tips:["Establish a consistent daily routine","Use quality hygiene products suited for your skin type","Pay attention to often-missed areas (behind ears, between toes)","Replace hygiene items regularly (toothbrush, razors)","Stay hydrated to support healthy skin and hair"],timeRecommendation:"20-45 minutes",frequency:"Daily"}},{id:"work",name:"Work",icon:"\uD83D\uDCBC",description:"Professional tasks and responsibilities",detailedInfo:{purpose:"Accomplish professional goals, contribute value, and advance career through focused work.",benefits:["Provides financial stability and security","Builds professional skills and experience","Creates sense of purpose and achievement","Develops problem-solving abilities","Expands professional network and opportunities"],tips:["Set clear daily and weekly goals","Prioritize tasks using time management techniques","Take regular breaks to maintain productivity","Minimize distractions during focused work time","Continuously learn and improve your skills"],timeRecommendation:"6-8 hours",frequency:"Weekdays"}},{id:"exercise",name:"Exercise",icon:"\uD83D\uDCAA",description:"Physical activity and fitness",detailedInfo:{purpose:"Maintain physical health, build strength, and improve overall well-being through regular exercise.",benefits:["Improves cardiovascular health and endurance","Builds muscle strength and bone density","Enhances mental health and reduces stress","Boosts energy levels and sleep quality","Helps maintain healthy weight and metabolism"],tips:["Start with activities you enjoy to build consistency","Gradually increase intensity and duration","Include both cardio and strength training","Stay hydrated before, during, and after exercise","Listen to your body and allow for rest days"],timeRecommendation:"30-60 minutes",frequency:"Daily or 5-6 times per week"}},{id:"nutrition",name:"Nutrition",icon:"\uD83E\uDD57",description:"Healthy eating and meal planning",detailedInfo:{purpose:"Fuel your body with nutritious foods to support optimal health, energy, and well-being.",benefits:["Provides essential nutrients for body functions","Maintains stable energy levels throughout the day","Supports immune system and disease prevention","Improves mental clarity and cognitive function","Helps maintain healthy weight and metabolism"],tips:["Plan meals in advance to avoid unhealthy choices","Include a variety of colorful fruits and vegetables","Stay hydrated with plenty of water","Practice portion control and mindful eating","Limit processed foods and added sugars"],timeRecommendation:"30-60 minutes for meal prep",frequency:"Daily meal planning and preparation"}},{id:"reflection",name:"Reflection",icon:"\uD83E\uDD14",description:"Daily journaling or self-reflection",detailedInfo:{purpose:"Gain self-awareness, process experiences, and promote personal growth through thoughtful reflection.",benefits:["Increases self-awareness and emotional intelligence","Helps process and learn from experiences","Reduces stress and promotes mental clarity","Identifies patterns and areas for improvement","Enhances gratitude and positive mindset"],tips:["Set aside quiet time without distractions","Write freely without worrying about grammar","Ask yourself meaningful questions about your day","Focus on both challenges and achievements","Review past entries to track growth and patterns"],timeRecommendation:"10-30 minutes",frequency:"Daily, preferably evening"}},{id:"connection",name:"Connection",icon:"\uD83D\uDC65",description:"Meaningful social interactions",detailedInfo:{purpose:"Build and maintain meaningful relationships that provide support, joy, and personal growth.",benefits:["Reduces feelings of loneliness and isolation","Provides emotional support and encouragement","Enhances mental health and well-being","Creates opportunities for learning and growth","Builds a strong support network for life challenges"],tips:["Be present and actively listen during conversations","Reach out to friends and family regularly","Engage in shared activities and interests","Show genuine interest in others' lives and experiences","Practice empathy and offer support when needed"],timeRecommendation:"30-60 minutes",frequency:"Daily interactions, deeper connections weekly"}},{id:"fasting",name:"Fasting",icon:"\uD83C\uDF19",description:"Spiritual fasting practice",daysOfWeek:[3,5],detailedInfo:{purpose:"Engage in spiritual discipline, self-control, and deeper connection with faith through fasting.",benefits:["Develops self-discipline and willpower","Enhances spiritual awareness and focus","Promotes gratitude for daily blessings","Encourages prayer and meditation","Builds empathy for those less fortunate"],tips:["Start with shorter fasting periods if new to fasting","Stay hydrated with water throughout the day","Use fasting time for prayer and reflection","Break your fast gently with light, healthy foods","Consult healthcare provider if you have medical conditions"],timeRecommendation:"Sunrise to sunset",frequency:"Wednesdays and Fridays"}}],a=()=>new Date(new Date().toLocaleString("en-US",{timeZone:"Africa/Lagos"})),o=e=>{let t=(e||a()).getDay();return s.filter(e=>!e.daysOfWeek||0===e.daysOfWeek.length||e.daysOfWeek.includes(t))},l=()=>o(),d=()=>a().toISOString().split("T")[0],c=()=>a().toISOString(),u=()=>new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",timeZone:"Africa/Lagos"}),m=(e,t)=>Math.round(e.length/t*100),p=e=>{let t=e.toLowerCase().replace(/\s+/g,"-"),r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r&=r;return`${t}-${Math.abs(r)}`},f={USERS:"routine_tracker_users"},h=e=>{},g=()=>[],x=e=>g().find(t=>t.name.toLowerCase()===e.toLowerCase())||null,y=e=>{},b=e=>{let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return Math.abs(t).toString()},v=(e,t)=>{let r=g(),n=r.findIndex(t=>t.id===e);-1!==n&&(r[n].pin=b(t),r[n].hasPinSetup=!0,localStorage.setItem(f.USERS,JSON.stringify(r)))},j=(e,t)=>{let r=g().find(t=>t.id===e);return!!r&&!!r.pin&&r.pin===b(t)},N=e=>{let t=g().find(t=>t.id===e);return!!(t&&t.hasPinSetup)},w=(e,t,r)=>{if(!j(e,t))return!1;let n=g(),i=n.findIndex(t=>t.id===e);return -1!==i&&(n[i].pin=b(r),n[i].hasPinSetup=!0,localStorage.setItem(f.USERS,JSON.stringify(n)),!0)},P=e=>{},R=()=>({}),E=e=>R()[`${e}_${d()}`]||null,k=(e,t)=>R()[`${e}_${t}`]||null,_=e=>{},S=e=>{if(!E(e)){let t=new Date;t.setDate(t.getDate()-1);let r=t.toISOString().split("T")[0],n=k(e,r);return n&&_({userId:n.userId,date:n.date,completedItems:n.completedItems,completionRate:Math.round(n.completedItems.length/8*100),streak:C(e,r)}),!0}return!1},C=(e,t)=>{let r=0,n=new Date(t);for(;;){let t=k(e,n.toISOString().split("T")[0]);if(t&&t.completedItems.length>0)r++,n.setDate(n.getDate()-1);else break}return r};function A({userId:e,onClose:t}){let[r,a]=(0,i.useState)([]),[o,l]=(0,i.useState)(!0),[d,c]=(0,i.useState)(0),u=()=>0===d?"This Week":1===d?"Last Week":`${d+1} Weeks Ago`,m=()=>r.reduce((e,t)=>t.completionRate>e.completionRate?t:e);return o?(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"}),(0,n.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading weekly report..."})]})}):(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-10 h-10 rounded-lg shadow-md"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"\uD83D\uDCCA Weekly Report"}),(0,n.jsx)("p",{className:"text-gray-600",children:u()})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{onClick:()=>c(d+1),className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"Previous week",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,n.jsx)("span",{className:"text-sm text-gray-600 min-w-[100px] text-center",children:u()}),(0,n.jsx)("button",{onClick:()=>c(Math.max(0,d-1)),disabled:0===d,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Next week",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 5l7 7-7 7"})})})]}),(0,n.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,n.jsxs)("div",{className:"bg-indigo-50 rounded-lg p-4 text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-indigo-600",children:[Math.round(r.reduce((e,t)=>e+t.completionRate,0)/r.length),"%"]}),(0,n.jsx)("div",{className:"text-sm text-indigo-700",children:"Weekly Average"})]}),(0,n.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[r.filter(e=>e.completionRate>0).length,"/7"]}),(0,n.jsx)("div",{className:"text-sm text-green-700",children:"Active Days"})]}),(0,n.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4 text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[m().completionRate,"%"]}),(0,n.jsxs)("div",{className:"text-sm text-purple-700",children:["Best Day (",m().dayName,")"]})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Daily Progress"}),(0,n.jsx)("div",{className:"grid grid-cols-7 gap-2",children:r.map((e,t)=>(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:e.dayName}),(0,n.jsx)("div",{className:"bg-gray-200 rounded-lg h-24 flex items-end justify-center p-1",children:(0,n.jsx)("div",{className:`w-full rounded transition-all duration-300 ${e.completionRate>=80?"bg-green-500":e.completionRate>=60?"bg-yellow-500":e.completionRate>=40?"bg-orange-500":e.completionRate>0?"bg-red-500":"bg-gray-300"}`,style:{height:`${Math.max(e.completionRate,5)}%`}})}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[e.completionRate,"%"]})]},t))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Task Performance"}),(0,n.jsx)("div",{className:"space-y-3",children:s.map(e=>{let t=r.filter(t=>t.progress?.completedItems.includes(e.id)).length;return{...e,completedDays:t,percentage:Math.round(t/7*100)}}).sort((e,t)=>t.percentage-e.percentage).map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("span",{className:"text-2xl",children:e.icon}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[e.completedDays,"/7 days completed"]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${e.percentage>=80?"bg-green-500":e.percentage>=60?"bg-yellow-500":e.percentage>=40?"bg-orange-500":e.percentage>0?"bg-red-500":"bg-gray-300"}`,style:{width:`${e.percentage}%`}})}),(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-700 w-12 text-right",children:[e.percentage,"%"]})]})]},e.id))})]})]}),(0,n.jsxs)("div",{className:"border-t border-gray-200 p-6 bg-gray-50",children:[(0,n.jsx)("button",{onClick:t,className:"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors mb-3",children:"Close Report"}),(0,n.jsxs)("p",{className:"text-center text-xs text-gray-500",children:["Built with ❤️ by ",(0,n.jsx)("span",{className:"font-semibold text-indigo-600",children:"Tech Talk"})]})]})]})})}function T({item:e,onClose:t}){let[r,s]=(0,i.useState)(!1),a=(0,i.useRef)(null),o=()=>{s(!1),setTimeout(()=>{t()},300)};return e.detailedInfo?(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300",style:{opacity:+!!r},children:(0,n.jsxs)("div",{ref:a,className:`bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 ${r?"translate-y-0 opacity-100":"translate-y-8 opacity-0"}`,children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"text-3xl",children:e.icon}),(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:e.name})]}),(0,n.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Purpose"}),(0,n.jsx)("p",{className:"text-gray-600",children:e.detailedInfo.purpose})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Benefits"}),(0,n.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:e.detailedInfo.benefits.map((e,t)=>(0,n.jsx)("li",{className:"text-gray-600",children:e},t))})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Tips"}),(0,n.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:e.detailedInfo.tips.map((e,t)=>(0,n.jsx)("li",{className:"text-gray-600",children:e},t))})]}),(0,n.jsx)("div",{className:"bg-indigo-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[e.detailedInfo.timeRecommendation&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-semibold text-indigo-800 mb-1",children:"Recommended Time"}),(0,n.jsx)("p",{className:"text-indigo-700",children:e.detailedInfo.timeRecommendation})]}),e.detailedInfo.frequency&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-semibold text-indigo-800 mb-1",children:"Frequency"}),(0,n.jsx)("p",{className:"text-indigo-700",children:e.detailedInfo.frequency})]})]})})]}),(0,n.jsx)("div",{className:"border-t border-gray-200 p-4 bg-gray-50 rounded-b-lg",children:(0,n.jsx)("button",{onClick:o,className:"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors",children:"Got it"})})]})}):null}function I({onSetPin:e,onSkip:t,userName:r}){let[s,a]=(0,i.useState)(""),[o,l]=(0,i.useState)(""),[d,c]=(0,i.useState)(""),[u,m]=(0,i.useState)(!1),p=async t=>{if(t.preventDefault(),c(""),4!==s.length)return void c("PIN must be exactly 4 digits");if(!/^\d{4}$/.test(s))return void c("PIN must contain only numbers");if(s!==o)return void c("PINs do not match");m(!0);try{e(s)}catch{c("Failed to set PIN. Please try again.")}finally{m(!1)}};return(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD10"})}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Secure Your Account"}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Hi ",r,"! Set up a 4-digit PIN to protect your progress from unauthorized access."]})]}),(0,n.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,n.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Why set a PIN?"}),(0,n.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,n.jsx)("li",{children:"• Prevent others from accessing your account"}),(0,n.jsx)("li",{children:"• Keep your progress private and secure"}),(0,n.jsx)("li",{children:"• Quick and easy to enter (just 4 digits)"})]})]}),(0,n.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"pin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter 4-digit PIN"}),(0,n.jsx)("input",{type:"password",id:"pin",value:s,onChange:e=>a(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:u,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"confirmPin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm PIN"}),(0,n.jsx)("input",{type:"password",id:"confirmPin",value:o,onChange:e=>l(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:u,required:!0})]}),d&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,n.jsx)("p",{className:"text-red-600 text-sm",children:d})}),(0,n.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,n.jsx)("button",{type:"button",onClick:t,disabled:u,className:"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Skip for now"}),(0,n.jsx)("button",{type:"submit",disabled:u||!s||!o,className:"flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:u?(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,n.jsx)("span",{children:"Setting up..."})]}):"Set PIN \uD83D\uDD12"})]})]}),(0,n.jsx)("p",{className:"text-xs text-gray-500 text-center mt-4",children:"You can change or remove your PIN anytime from the menu"})]})})}function O({onChangePin:e,onCancel:t,userName:r}){let[s,a]=(0,i.useState)(""),[o,l]=(0,i.useState)(""),[d,c]=(0,i.useState)(""),[u,m]=(0,i.useState)(""),[p,f]=(0,i.useState)(!1),h=async t=>{if(t.preventDefault(),m(""),4!==s.length)return void m("Current PIN must be exactly 4 digits");if(!/^\d{4}$/.test(s))return void m("Current PIN must contain only numbers");if(4!==o.length)return void m("New PIN must be exactly 4 digits");if(!/^\d{4}$/.test(o))return void m("New PIN must contain only numbers");if(o!==d)return void m("New PINs do not match");if(s===o)return void m("New PIN must be different from current PIN");f(!0);try{await e(s,o)||m("Current PIN is incorrect. Please try again.")}catch{m("Failed to change PIN. Please try again.")}finally{f(!1)}};return(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD10"})}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Change Your PIN"}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Hi ",r,"! Enter your current PIN and set a new 4-digit PIN."]})]}),u&&(0,n.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,n.jsx)("p",{className:"text-red-600 text-sm",children:u})}),(0,n.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"currentPin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Current PIN"}),(0,n.jsx)("input",{type:"password",id:"currentPin",value:s,onChange:e=>a(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:p,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"newPin",className:"block text-sm font-medium text-gray-700 mb-2",children:"New PIN"}),(0,n.jsx)("input",{type:"password",id:"newPin",value:o,onChange:e=>l(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:p,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"confirmNewPin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New PIN"}),(0,n.jsx)("input",{type:"password",id:"confirmNewPin",value:d,onChange:e=>c(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:p,required:!0})]}),(0,n.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,n.jsx)("button",{type:"button",onClick:t,disabled:p,className:"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Cancel"}),(0,n.jsx)("button",{type:"submit",disabled:p||!s||!o||!d,className:"flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:p?(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,n.jsx)("span",{children:"Changing..."})]}):"Change PIN \uD83D\uDD12"})]})]}),(0,n.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,n.jsx)("p",{className:"text-blue-700 text-xs",children:"\uD83D\uDD12 Your PIN is securely stored and protects your routine data from unauthorized access."})})]})})}function D({userId:e,onLogout:t}){let[r,s]=(0,i.useState)(null),[a,o]=(0,i.useState)(!0),[p,f]=(0,i.useState)(!1),[h,g]=(0,i.useState)(null),[x,b]=(0,i.useState)(!1),[j,R]=(0,i.useState)(!1),[k,_]=(0,i.useState)(l()),[C,D]=(0,i.useState)(null),[M,$]=(0,i.useState)(!1),[L,U]=(0,i.useState)(!1),[q,W]=(0,i.useState)(!1),z=async()=>{o(!0);try{S(e);let t=JSON.parse(localStorage.getItem("routine_tracker_users")||"[]").find(t=>t.id===e);g(t);let r=E(e);if(r)s(r);else{let t={userId:e,date:d(),completedItems:[],lastUpdated:c()};s(t),P(t)}N(e)||setTimeout(()=>{$(!0)},1e3)}catch(e){console.error("Error initializing tracker:",e)}finally{o(!1)}},F=async e=>{if(r&&!p){f(!0);try{let t=r.completedItems.includes(e)?r.completedItems.filter(t=>t!==e):[...r.completedItems,e],n={...r,completedItems:t,lastUpdated:c()};s(n),P(n)}catch(e){console.error("Error updating progress:",e)}finally{f(!1)}}},B=e=>{D(e)};if(a)return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 mb-2",children:"Loading your tracker..."}),(0,n.jsx)("p",{className:"text-sm text-indigo-600 font-semibold",children:"Built with ❤️ by Tech Talk"})]})});if(!r)return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-red-600",children:"Error loading your progress. Please try again."}),(0,n.jsx)("button",{onClick:z,className:"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",children:"Retry"})]})});let H=m(r.completedItems,k.length),X=u();return(0,n.jsx)("div",{className:"min-h-screen p-4",children:(0,n.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-12 h-12 rounded-lg shadow-md"}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",h?.name||"Friend","! \uD83D\uDC4B"]}),(0,n.jsx)("p",{className:"text-gray-600",children:X})]})]}),(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("button",{onClick:()=>{b(!x)},className:"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",title:"Menu",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})}),x&&(0,n.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10",children:[(0,n.jsxs)("button",{onClick:()=>{R(!0),b(!1)},className:"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2",children:[(0,n.jsx)("span",{children:"\uD83D\uDCCA"}),(0,n.jsx)("span",{children:"Weekly Report"})]}),(0,n.jsxs)("button",{onClick:()=>{U(!0),b(!1)},className:"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2",children:[(0,n.jsx)("span",{children:"\uD83D\uDD10"}),(0,n.jsx)("span",{children:"Change PIN"})]}),(0,n.jsx)("div",{className:"border-t border-gray-100 my-1"}),(0,n.jsxs)("button",{onClick:()=>{y(""),t()},className:"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2",children:[(0,n.jsx)("span",{children:"\uD83D\uDC64"}),(0,n.jsx)("span",{children:"Switch User"})]})]})]})})]}),q&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("span",{className:"text-2xl",children:"✅"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-green-800 font-medium",children:"PIN Changed Successfully!"}),(0,n.jsx)("p",{className:"text-green-600 text-sm",children:"Your account is now secured with your new PIN."})]})]}),(0,n.jsxs)("div",{className:"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Todays Progress"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-indigo-600",children:[r.completedItems.length,"/",k.length]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Completion Rate"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-indigo-600",children:[H,"%"]})]})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("div",{className:"bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full transition-all duration-300",style:{width:`${H}%`}})})})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Daily Routine Checklist ✅"}),(0,n.jsx)("div",{className:"space-y-3",children:k.map(e=>{let t=r.completedItems.includes(e.id);return(0,n.jsxs)("div",{className:`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${t?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50"}`,onClick:()=>F(e.id),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,n.jsx)("div",{className:"text-2xl",children:e.icon}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h3",{className:`font-medium ${t?"text-green-800":"text-gray-800"}`,children:e.name}),(0,n.jsx)("p",{className:`text-sm ${t?"text-green-600":"text-gray-600"}`,children:e.description})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[e.detailedInfo&&(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),B(e)},className:"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors",title:"More info",children:(0,n.jsx)("span",{className:"text-indigo-600 text-sm font-bold",children:"ⓘ"})}),(0,n.jsx)("div",{className:`w-6 h-6 rounded-full border-2 flex items-center justify-center ${t?"border-green-500 bg-green-500":"border-gray-300"}`,children:t&&(0,n.jsx)("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})]},e.id)})}),p&&(0,n.jsx)("div",{className:"mt-4 text-center text-sm text-gray-600",children:(0,n.jsxs)("div",{className:"inline-flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"}),(0,n.jsx)("span",{children:"Saving..."})]})})]}),(0,n.jsxs)("div",{className:"text-center mt-6 text-gray-600",children:[(0,n.jsx)("p",{className:"text-sm",children:100===H?"\uD83C\uDF89 Amazing! Youve completed all your routines today!":H>=75?"\uD83D\uDD25 Youre doing great! Keep it up!":H>=50?"\uD83D\uDCAA Good progress! Youre halfway there!":"\uD83C\uDF31 Every step counts. Youve got this!"}),(0,n.jsx)("p",{className:"text-xs mt-2",children:"Progress auto-saves • Resets at midnight • Access Weekly Report from menu"}),(0,n.jsxs)("p",{className:"text-xs mt-2",children:["Built with ❤️ by ",(0,n.jsx)("span",{className:"font-semibold text-indigo-600",children:"Tech Talk"})]})]}),j&&(0,n.jsx)(A,{userId:e,onClose:()=>{R(!1)}}),C&&(0,n.jsx)(T,{item:C,onClose:()=>{D(null)}}),M&&h&&(0,n.jsx)(I,{userName:h.name,onSetPin:t=>{v(e,t),$(!1)},onSkip:()=>{$(!1)}}),L&&h&&(0,n.jsx)(O,{userName:h.name,onChangePin:(t,r)=>{let n=w(e,t,r);return n&&(U(!1),W(!0),setTimeout(()=>{W(!1)},3e3)),n},onCancel:()=>{U(!1)}})]})})}let M=[{name:"Obinna",displayName:"Mchost",role:"admin",joinDate:"2025-01-10"},{name:"Daniel",displayName:"Aj danny Roze",role:"user",joinDate:"2025-01-10"}],$=e=>{let t=e.toLowerCase().trim();return M.some(e=>e.name.toLowerCase()===t)},L=e=>{let t=e.toLowerCase().trim();return M.find(e=>e.name.toLowerCase()===t)||null},U=e=>{let t=L(e);return t?.displayName||e};function q({onUserLogin:e}){let[t,r]=(0,i.useState)(""),[s,a]=(0,i.useState)(!1),[o,l]=(0,i.useState)(""),d=async r=>{if(r.preventDefault(),t.trim()){a(!0),l("");try{if(!$(t.trim())){l("Access denied. You are not authorized to use this tracker."),a(!1);return}let r=U(t.trim()),n=x(t.trim())||x(r);n?(n.lastActive=c(),n.name=r):n={id:p(t.trim()),name:r,createdAt:c(),lastActive:c()},h(n),y(n.id),e(n.id)}catch(e){console.error("Error logging in:",e),l("An error occurred. Please try again.")}finally{a(!1)}}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"max-w-md w-full",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-20 h-20 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Digital Routine & Results Tracker"}),(0,n.jsx)("p",{className:"text-gray-600 text-lg",children:"Track your daily growth with intention"}),(0,n.jsx)("p",{className:"text-sm text-indigo-600 font-semibold mt-2",children:"Built with ❤️ by Tech Talk"})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"What were building here \uD83D\uDCBB✅"}),(0,n.jsxs)("div",{className:"space-y-3 text-gray-600",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Type in your name"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Tick what youve done for the day (Prayer, Study, Hygiene, Work, etc.)"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Submit, and it saves your progress"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Come back anytime before the day ends to update it"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"System resets at midnight, but keeps a history of your growth"})]})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Ready to track your growth? \uD83D\uDCCA✨"}),(0,n.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),(0,n.jsx)("input",{type:"text",id:"name",value:t,onChange:e=>r(e.target.value),placeholder:"Enter your name...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors",disabled:s,required:!0})]}),o&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,n.jsx)("p",{className:"text-red-600 text-sm",children:o})}),(0,n.jsx)("button",{type:"submit",disabled:s||!t.trim(),className:"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:s?(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,n.jsx)("span",{children:"Getting started..."})]}):"Start Tracking \uD83D\uDD25"})]})]}),(0,n.jsx)("div",{className:"text-center mt-6 text-gray-500 text-sm",children:(0,n.jsx)("p",{children:"No login stress. No judgment. Just you, your goals, and your growth."})})]})})}function W({userName:e,onPinSubmit:t,onBack:r,error:s}){let[a,o]=(0,i.useState)(""),[l,d]=(0,i.useState)(!1),c=e=>{a.length<4&&o(t=>t+e)};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,n.jsxs)("div",{className:"max-w-md w-full",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:["Welcome back, ",e,"! \uD83D\uDC4B"]}),(0,n.jsx)("p",{className:"text-gray-600",children:"Enter your 4-digit PIN to continue"})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsx)("div",{className:"flex justify-center space-x-4 mb-6",children:[0,1,2,3].map(e=>(0,n.jsx)("div",{className:`w-12 h-12 rounded-lg border-2 flex items-center justify-center text-2xl font-bold ${a.length>e?"border-indigo-500 bg-indigo-50 text-indigo-600":"border-gray-300 bg-gray-50"}`,children:a.length>e?"•":""},e))}),s&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,n.jsx)("p",{className:"text-red-600 text-sm text-center",children:s})}),(0,n.jsx)("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[1,2,3,4,5,6,7,8,9].map(e=>(0,n.jsx)("button",{onClick:()=>c(e.toString()),disabled:a.length>=4||l,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:e},e))}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-3 mb-6",children:[(0,n.jsx)("button",{onClick:()=>{o("")},disabled:0===a.length||l,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Clear"}),(0,n.jsx)("button",{onClick:()=>c("0"),disabled:a.length>=4||l,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"0"}),(0,n.jsx)("button",{onClick:()=>{o(e=>e.slice(0,-1))},disabled:0===a.length||l,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"⌫"})]}),l&&(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("div",{className:"inline-flex items-center space-x-2 text-indigo-600",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"}),(0,n.jsx)("span",{className:"text-sm",children:"Verifying..."})]})})]}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("button",{onClick:r,disabled:l,className:"text-indigo-600 hover:text-indigo-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:"← Back to name entry"})}),(0,n.jsx)("div",{className:"text-center mt-6",children:(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Built with ❤️ by Tech Talk"})})]})})}function z(){let[e,t]=(0,i.useState)(null),[r,s]=(0,i.useState)(!0),[a,o]=(0,i.useState)(!1),[l,d]=(0,i.useState)(null),[c,u]=(0,i.useState)("");return r?(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 mb-2",children:"Loading your routine tracker..."}),(0,n.jsx)("p",{className:"text-sm text-indigo-600 font-semibold",children:"Built with ❤️ by Tech Talk"})]})}):a&&l?(0,n.jsx)(W,{userName:(()=>{if(!l)return"";let e=g().find(e=>e.id===l);return e?.name||""})(),onPinSubmit:e=>{l&&(j(l,e)?(t(l),o(!1),d(null),u("")):u("Incorrect PIN. Please try again."))},onBack:()=>{o(!1),d(null),u("")},error:c}):(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:e?(0,n.jsx)(D,{userId:e,onLogout:()=>t(null)}):(0,n.jsx)(q,{onUserLogin:e=>{N(e)?(d(e),o(!0),u("")):t(e)}})})}},8795:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9368:(e,t,r)=>{Promise.resolve().then(r.bind(r,8659))},9551:e=>{"use strict";e.exports=require("url")},9603:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,145],()=>r(1759));module.exports=n})();