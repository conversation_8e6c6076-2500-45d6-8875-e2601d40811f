[{"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx": "3", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx": "4", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx": "5", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx": "6", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts": "7", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts": "9", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts": "10", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinInputScreen.tsx": "11", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinSetupPopup.tsx": "12", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\ChangePinPopup.tsx": "13"}, {"size": 689, "mtime": 1752249449047, "results": "14", "hashOfConfig": "15"}, {"size": 3121, "mtime": 1752250512136, "results": "16", "hashOfConfig": "15"}, {"size": 16257, "mtime": 1752274203001, "results": "17", "hashOfConfig": "15"}, {"size": 4791, "mtime": 1752158286771, "results": "18", "hashOfConfig": "15"}, {"size": 10123, "mtime": 1752166006347, "results": "19", "hashOfConfig": "15"}, {"size": 6205, "mtime": 1752250227082, "results": "20", "hashOfConfig": "15"}, {"size": 1918, "mtime": 1752157934473, "results": "21", "hashOfConfig": "15"}, {"size": 12024, "mtime": 1752250357146, "results": "22", "hashOfConfig": "15"}, {"size": 5172, "mtime": 1752187602092, "results": "23", "hashOfConfig": "15"}, {"size": 7174, "mtime": 1752273564925, "results": "24", "hashOfConfig": "15"}, {"size": 5321, "mtime": 1752251478628, "results": "25", "hashOfConfig": "15"}, {"size": 5259, "mtime": 1752273139946, "results": "26", "hashOfConfig": "15"}, {"size": 6293, "mtime": 1752273645188, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9cy87n", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx", ["67"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx", ["68", "69", "70"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx", ["71", "72"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx", ["73", "74"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx", ["75"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinInputScreen.tsx", ["76", "77"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinSetupPopup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\ChangePinPopup.tsx", [], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 68, "column": 11, "nodeType": "80", "endLine": 72, "endColumn": 13}, {"ruleId": "81", "severity": 1, "message": "82", "line": 47, "column": 6, "nodeType": "83", "endLine": 47, "endColumn": 14, "suggestions": "84"}, {"ruleId": "78", "severity": 1, "message": "79", "line": 212, "column": 11, "nodeType": "80", "endLine": 216, "endColumn": 13}, {"ruleId": "78", "severity": 1, "message": "79", "line": 253, "column": 15, "nodeType": "80", "endLine": 257, "endColumn": 17}, {"ruleId": "81", "severity": 1, "message": "85", "line": 31, "column": 6, "nodeType": "83", "endLine": 31, "endColumn": 8, "suggestions": "86"}, {"ruleId": "81", "severity": 1, "message": "85", "line": 46, "column": 6, "nodeType": "83", "endLine": 46, "endColumn": 8, "suggestions": "87"}, {"ruleId": "81", "severity": 1, "message": "88", "line": 26, "column": 6, "nodeType": "83", "endLine": 26, "endColumn": 28, "suggestions": "89"}, {"ruleId": "78", "severity": 1, "message": "79", "line": 115, "column": 13, "nodeType": "80", "endLine": 119, "endColumn": 15}, {"ruleId": "78", "severity": 1, "message": "79", "line": 70, "column": 11, "nodeType": "80", "endLine": 74, "endColumn": 13}, {"ruleId": "81", "severity": 1, "message": "90", "line": 21, "column": 6, "nodeType": "83", "endLine": 21, "endColumn": 11, "suggestions": "91"}, {"ruleId": "78", "severity": 1, "message": "79", "line": 55, "column": 11, "nodeType": "80", "endLine": 59, "endColumn": 13}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeTracker'. Either include it or remove the dependency array.", "ArrayExpression", ["92"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["93"], ["94"], "React Hook useEffect has a missing dependency: 'loadWeeklyData'. Either include it or remove the dependency array.", ["95"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["96"], {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, {"desc": "99", "fix": "101"}, {"desc": "102", "fix": "103"}, {"desc": "104", "fix": "105"}, "Update the dependencies array to be: [initializeTracker, userId]", {"range": "106", "text": "107"}, "Update the dependencies array to be: [handleClose]", {"range": "108", "text": "109"}, {"range": "110", "text": "109"}, "Update the dependencies array to be: [userId, selectedWeek, loadWeeklyData]", {"range": "111", "text": "112"}, "Update the dependencies array to be: [handleSubmit, pin]", {"range": "113", "text": "114"}, [1551, 1559], "[initializeTracker, userId]", [816, 818], "[handleClose]", [1150, 1152], [729, 751], "[userId, selectedWeek, loadWeeklyData]", [506, 511], "[handleSubmit, pin]"]