(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2128:(e,t,s)=>{Promise.resolve().then(s.bind(s,9877))},9877:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var n=s(5155),i=s(2115);let a=[{id:"prayer",name:"Prayer",icon:"\uD83D\uDE4F",description:"Daily spiritual practice and reflection",detailedInfo:{purpose:"Connect with the divine, find inner peace, and seek guidance for daily life.",benefits:["Reduces stress and anxiety","Provides spiritual guidance and clarity","Strengthens faith and spiritual connection","Promotes gratitude and mindfulness","Offers comfort during difficult times"],tips:["Set aside a quiet, dedicated space for prayer","Choose a consistent time each day","Start with gratitude and thanksgiving","Include prayers for others, not just yourself","Listen for guidance and inner peace"],timeRecommendation:"10-30 minutes",frequency:"Daily"}},{id:"study",name:"Study",icon:"\uD83D\uDCDA",description:"Learning, reading, or skill development",detailedInfo:{purpose:"Expand knowledge, develop skills, and pursue personal or professional growth through dedicated learning.",benefits:["Improves cognitive function and memory","Enhances career prospects and opportunities","Builds confidence and self-esteem","Keeps mind sharp and engaged","Opens new perspectives and ideas"],tips:["Create a distraction-free study environment","Use active learning techniques (notes, summaries)","Take regular breaks to maintain focus","Set specific, achievable learning goals","Review and practice regularly for retention"],timeRecommendation:"30-120 minutes",frequency:"Daily"}},{id:"hygiene",name:"Hygiene",icon:"\uD83E\uDDFC",description:"Personal care and cleanliness",detailedInfo:{purpose:"Maintain personal cleanliness, health, and confidence through proper hygiene practices.",benefits:["Prevents illness and infections","Boosts self-confidence and social acceptance","Improves overall health and well-being","Creates positive first impressions","Reduces stress and anxiety about appearance"],tips:["Establish a consistent daily routine","Use quality hygiene products suited for your skin type","Pay attention to often-missed areas (behind ears, between toes)","Replace hygiene items regularly (toothbrush, razors)","Stay hydrated to support healthy skin and hair"],timeRecommendation:"20-45 minutes",frequency:"Daily"}},{id:"work",name:"Work",icon:"\uD83D\uDCBC",description:"Professional tasks and responsibilities",detailedInfo:{purpose:"Accomplish professional goals, contribute value, and advance career through focused work.",benefits:["Provides financial stability and security","Builds professional skills and experience","Creates sense of purpose and achievement","Develops problem-solving abilities","Expands professional network and opportunities"],tips:["Set clear daily and weekly goals","Prioritize tasks using time management techniques","Take regular breaks to maintain productivity","Minimize distractions during focused work time","Continuously learn and improve your skills"],timeRecommendation:"6-8 hours",frequency:"Weekdays"}},{id:"exercise",name:"Exercise",icon:"\uD83D\uDCAA",description:"Physical activity and fitness",detailedInfo:{purpose:"Maintain physical health, build strength, and improve overall well-being through regular exercise.",benefits:["Improves cardiovascular health and endurance","Builds muscle strength and bone density","Enhances mental health and reduces stress","Boosts energy levels and sleep quality","Helps maintain healthy weight and metabolism"],tips:["Start with activities you enjoy to build consistency","Gradually increase intensity and duration","Include both cardio and strength training","Stay hydrated before, during, and after exercise","Listen to your body and allow for rest days"],timeRecommendation:"30-60 minutes",frequency:"Daily or 5-6 times per week"}},{id:"nutrition",name:"Nutrition",icon:"\uD83E\uDD57",description:"Healthy eating and meal planning",detailedInfo:{purpose:"Fuel your body with nutritious foods to support optimal health, energy, and well-being.",benefits:["Provides essential nutrients for body functions","Maintains stable energy levels throughout the day","Supports immune system and disease prevention","Improves mental clarity and cognitive function","Helps maintain healthy weight and metabolism"],tips:["Plan meals in advance to avoid unhealthy choices","Include a variety of colorful fruits and vegetables","Stay hydrated with plenty of water","Practice portion control and mindful eating","Limit processed foods and added sugars"],timeRecommendation:"30-60 minutes for meal prep",frequency:"Daily meal planning and preparation"}},{id:"reflection",name:"Reflection",icon:"\uD83E\uDD14",description:"Daily journaling or self-reflection",detailedInfo:{purpose:"Gain self-awareness, process experiences, and promote personal growth through thoughtful reflection.",benefits:["Increases self-awareness and emotional intelligence","Helps process and learn from experiences","Reduces stress and promotes mental clarity","Identifies patterns and areas for improvement","Enhances gratitude and positive mindset"],tips:["Set aside quiet time without distractions","Write freely without worrying about grammar","Ask yourself meaningful questions about your day","Focus on both challenges and achievements","Review past entries to track growth and patterns"],timeRecommendation:"10-30 minutes",frequency:"Daily, preferably evening"}},{id:"connection",name:"Connection",icon:"\uD83D\uDC65",description:"Meaningful social interactions",detailedInfo:{purpose:"Build and maintain meaningful relationships that provide support, joy, and personal growth.",benefits:["Reduces feelings of loneliness and isolation","Provides emotional support and encouragement","Enhances mental health and well-being","Creates opportunities for learning and growth","Builds a strong support network for life challenges"],tips:["Be present and actively listen during conversations","Reach out to friends and family regularly","Engage in shared activities and interests","Show genuine interest in others' lives and experiences","Practice empathy and offer support when needed"],timeRecommendation:"30-60 minutes",frequency:"Daily interactions, deeper connections weekly"}},{id:"fasting",name:"Fasting",icon:"\uD83C\uDF19",description:"Spiritual fasting practice",daysOfWeek:[3,5],detailedInfo:{purpose:"Engage in spiritual discipline, self-control, and deeper connection with faith through fasting.",benefits:["Develops self-discipline and willpower","Enhances spiritual awareness and focus","Promotes gratitude for daily blessings","Encourages prayer and meditation","Builds empathy for those less fortunate"],tips:["Start with shorter fasting periods if new to fasting","Stay hydrated with water throughout the day","Use fasting time for prayer and reflection","Break your fast gently with light, healthy foods","Consult healthcare provider if you have medical conditions"],timeRecommendation:"Sunrise to sunset",frequency:"Wednesdays and Fridays"}}],r=()=>new Date(new Date().toLocaleString("en-US",{timeZone:"Africa/Lagos"})),l=e=>{let t=(e||r()).getDay();return a.filter(e=>!e.daysOfWeek||0===e.daysOfWeek.length||e.daysOfWeek.includes(t))},o=()=>l(),d=()=>r().toISOString().split("T")[0],c=()=>r().toISOString(),m=()=>new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",timeZone:"Africa/Lagos"}),u=(e,t)=>Math.round(e.length/t*100),x=e=>{let t=e.toLowerCase().replace(/\s+/g,"-"),s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t),s&=s;return"".concat(t,"-").concat(Math.abs(s))},g={USERS:"routine_tracker_users",DAILY_PROGRESS:"routine_tracker_daily_progress",HISTORICAL_DATA:"routine_tracker_historical_data",CURRENT_USER:"routine_tracker_current_user"},h=e=>{let t=p(),s=t.findIndex(t=>t.id===e.id);s>=0?t[s]=e:t.push(e),localStorage.setItem(g.USERS,JSON.stringify(t))},p=()=>{let e=localStorage.getItem(g.USERS);return e?JSON.parse(e):[]},f=e=>p().find(t=>t.name.toLowerCase()===e.toLowerCase())||null,y=e=>{localStorage.setItem(g.CURRENT_USER,e)},b=()=>localStorage.getItem(g.CURRENT_USER),j=e=>{let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return Math.abs(t).toString()},v=(e,t)=>{let s=p(),n=s.findIndex(t=>t.id===e);-1!==n&&(s[n].pin=j(t),s[n].hasPinSetup=!0,localStorage.setItem(g.USERS,JSON.stringify(s)))},N=(e,t)=>{let s=p().find(t=>t.id===e);return!!s&&!!s.pin&&s.pin===j(t)},w=e=>{let t=p().find(t=>t.id===e);return!!(t&&t.hasPinSetup)},k=e=>{let t=S();t["".concat(e.userId,"_").concat(e.date)]=e,localStorage.setItem(g.DAILY_PROGRESS,JSON.stringify(t))},S=()=>{let e=localStorage.getItem(g.DAILY_PROGRESS);return e?JSON.parse(e):{}},I=e=>S()["".concat(e,"_").concat(d())]||null,R=(e,t)=>S()["".concat(e,"_").concat(t)]||null,D=e=>{let t=C();t["".concat(e.userId,"_").concat(e.date)]=e,localStorage.setItem(g.HISTORICAL_DATA,JSON.stringify(t))},C=()=>{let e=localStorage.getItem(g.HISTORICAL_DATA);return e?JSON.parse(e):{}},P=e=>{let t=d(),s=I(e);if(s&&s.date!==t){D({userId:s.userId,date:s.date,completedItems:s.completedItems,completionRate:Math.round(s.completedItems.length/8*100),streak:E(e,s.date)});let t=S(),n="".concat(e,"_").concat(s.date);return delete t[n],localStorage.setItem(g.DAILY_PROGRESS,JSON.stringify(t)),!0}return!1},E=(e,t)=>{let s=0,n=new Date(t);for(;;){let t=R(e,n.toISOString().split("T")[0]);if(t&&t.completedItems.length>0)s++,n.setDate(n.getDate()-1);else break}return s};function L(e){let{userId:t,onClose:s}=e,[r,l]=(0,i.useState)([]),[o,d]=(0,i.useState)(!0),[c,m]=(0,i.useState)(0);(0,i.useEffect)(()=>{x()},[t,c]);let x=()=>{d(!0);let e=[],s=new Date,n=s.getDay(),i=new Date(s);i.setDate(s.getDate()-n-7*c);for(let s=0;s<7;s++){let n=new Date(i);n.setDate(i.getDate()+s);let r=n.toISOString().split("T")[0],l=R(t,r),o=l?u(l.completedItems,a.length):0;e.push({date:r,dayName:n.toLocaleDateString("en-US",{weekday:"short"}),progress:l,completionRate:o})}l(e),d(!1)},g=()=>0===c?"This Week":1===c?"Last Week":"".concat(c+1," Weeks Ago"),h=()=>r.reduce((e,t)=>t.completionRate>e.completionRate?t:e);return o?(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg p-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"}),(0,n.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading weekly report..."})]})}):(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-10 h-10 rounded-lg shadow-md"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"\uD83D\uDCCA Weekly Report"}),(0,n.jsx)("p",{className:"text-gray-600",children:g()})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{onClick:()=>m(c+1),className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"Previous week",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,n.jsx)("span",{className:"text-sm text-gray-600 min-w-[100px] text-center",children:g()}),(0,n.jsx)("button",{onClick:()=>m(Math.max(0,c-1)),disabled:0===c,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Next week",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 5l7 7-7 7"})})})]}),(0,n.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})]}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,n.jsxs)("div",{className:"bg-indigo-50 rounded-lg p-4 text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-indigo-600",children:[Math.round(r.reduce((e,t)=>e+t.completionRate,0)/r.length),"%"]}),(0,n.jsx)("div",{className:"text-sm text-indigo-700",children:"Weekly Average"})]}),(0,n.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[r.filter(e=>e.completionRate>0).length,"/7"]}),(0,n.jsx)("div",{className:"text-sm text-green-700",children:"Active Days"})]}),(0,n.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4 text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[h().completionRate,"%"]}),(0,n.jsxs)("div",{className:"text-sm text-purple-700",children:["Best Day (",h().dayName,")"]})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Daily Progress"}),(0,n.jsx)("div",{className:"grid grid-cols-7 gap-2",children:r.map((e,t)=>(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:e.dayName}),(0,n.jsx)("div",{className:"bg-gray-200 rounded-lg h-24 flex items-end justify-center p-1",children:(0,n.jsx)("div",{className:"w-full rounded transition-all duration-300 ".concat(e.completionRate>=80?"bg-green-500":e.completionRate>=60?"bg-yellow-500":e.completionRate>=40?"bg-orange-500":e.completionRate>0?"bg-red-500":"bg-gray-300"),style:{height:"".concat(Math.max(e.completionRate,5),"%")}})}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[e.completionRate,"%"]})]},t))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Task Performance"}),(0,n.jsx)("div",{className:"space-y-3",children:a.map(e=>{let t=r.filter(t=>{var s;return null==(s=t.progress)?void 0:s.completedItems.includes(e.id)}).length;return{...e,completedDays:t,percentage:Math.round(t/7*100)}}).sort((e,t)=>t.percentage-e.percentage).map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("span",{className:"text-2xl",children:e.icon}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,n.jsxs)("div",{className:"text-sm text-gray-600",children:[e.completedDays,"/7 days completed"]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(e.percentage>=80?"bg-green-500":e.percentage>=60?"bg-yellow-500":e.percentage>=40?"bg-orange-500":e.percentage>0?"bg-red-500":"bg-gray-300"),style:{width:"".concat(e.percentage,"%")}})}),(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-700 w-12 text-right",children:[e.percentage,"%"]})]})]},e.id))})]})]}),(0,n.jsxs)("div",{className:"border-t border-gray-200 p-6 bg-gray-50",children:[(0,n.jsx)("button",{onClick:s,className:"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors mb-3",children:"Close Report"}),(0,n.jsxs)("p",{className:"text-center text-xs text-gray-500",children:["Built with ❤️ by ",(0,n.jsx)("span",{className:"font-semibold text-indigo-600",children:"Tech Talk"})]})]})]})})}function T(e){let{item:t,onClose:s}=e,[a,r]=(0,i.useState)(!1),l=(0,i.useRef)(null);(0,i.useEffect)(()=>{r(!0);let e=e=>{l.current&&!l.current.contains(e.target)&&o()};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,i.useEffect)(()=>{let e=e=>{"Escape"===e.key&&o()};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[]);let o=()=>{r(!1),setTimeout(()=>{s()},300)};return t.detailedInfo?(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300",style:{opacity:+!!a},children:(0,n.jsxs)("div",{ref:l,className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 ".concat(a?"translate-y-0 opacity-100":"translate-y-8 opacity-0"),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"text-3xl",children:t.icon}),(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:t.name})]}),(0,n.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Purpose"}),(0,n.jsx)("p",{className:"text-gray-600",children:t.detailedInfo.purpose})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Benefits"}),(0,n.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:t.detailedInfo.benefits.map((e,t)=>(0,n.jsx)("li",{className:"text-gray-600",children:e},t))})]}),(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Tips"}),(0,n.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:t.detailedInfo.tips.map((e,t)=>(0,n.jsx)("li",{className:"text-gray-600",children:e},t))})]}),(0,n.jsx)("div",{className:"bg-indigo-50 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[t.detailedInfo.timeRecommendation&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-semibold text-indigo-800 mb-1",children:"Recommended Time"}),(0,n.jsx)("p",{className:"text-indigo-700",children:t.detailedInfo.timeRecommendation})]}),t.detailedInfo.frequency&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-semibold text-indigo-800 mb-1",children:"Frequency"}),(0,n.jsx)("p",{className:"text-indigo-700",children:t.detailedInfo.frequency})]})]})})]}),(0,n.jsx)("div",{className:"border-t border-gray-200 p-4 bg-gray-50 rounded-b-lg",children:(0,n.jsx)("button",{onClick:o,className:"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors",children:"Got it"})})]})}):null}function A(e){let{onSetPin:t,onSkip:s,userName:a}=e,[r,l]=(0,i.useState)(""),[o,d]=(0,i.useState)(""),[c,m]=(0,i.useState)(""),[u,x]=(0,i.useState)(!1),g=async e=>{if(e.preventDefault(),m(""),4!==r.length)return void m("PIN must be exactly 4 digits");if(!/^\d{4}$/.test(r))return void m("PIN must contain only numbers");if(r!==o)return void m("PINs do not match");x(!0);try{t(r)}catch(e){m("Failed to set PIN. Please try again.")}finally{x(!1)}};return(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD10"})}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Secure Your Account"}),(0,n.jsxs)("p",{className:"text-gray-600",children:["Hi ",a,"! Set up a 4-digit PIN to protect your progress from unauthorized access."]})]}),(0,n.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,n.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Why set a PIN?"}),(0,n.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,n.jsx)("li",{children:"• Prevent others from accessing your account"}),(0,n.jsx)("li",{children:"• Keep your progress private and secure"}),(0,n.jsx)("li",{children:"• Quick and easy to enter (just 4 digits)"})]})]}),(0,n.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"pin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Enter 4-digit PIN"}),(0,n.jsx)("input",{type:"password",id:"pin",value:r,onChange:e=>l(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:u,required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"confirmPin",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm PIN"}),(0,n.jsx)("input",{type:"password",id:"confirmPin",value:o,onChange:e=>d(e.target.value.slice(0,4)),placeholder:"••••",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest",maxLength:4,pattern:"[0-9]{4}",disabled:u,required:!0})]}),c&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,n.jsx)("p",{className:"text-red-600 text-sm",children:c})}),(0,n.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,n.jsx)("button",{type:"button",onClick:s,disabled:u,className:"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Skip for now"}),(0,n.jsx)("button",{type:"submit",disabled:u||!r||!o,className:"flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:u?(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,n.jsx)("span",{children:"Setting up..."})]}):"Set PIN \uD83D\uDD12"})]})]}),(0,n.jsx)("p",{className:"text-xs text-gray-500 text-center mt-4",children:"You can change or remove your PIN anytime from the menu"})]})})}function _(e){let{userId:t,onLogout:s}=e,[a,r]=(0,i.useState)(null),[l,x]=(0,i.useState)(!0),[g,h]=(0,i.useState)(!1),[p,f]=(0,i.useState)(null),[b,j]=(0,i.useState)(!1),[N,S]=(0,i.useState)(!1),[R,D]=(0,i.useState)(o()),[C,E]=(0,i.useState)(null),[_,B]=(0,i.useState)(!1);(0,i.useEffect)(()=>{O()},[t]),(0,i.useEffect)(()=>{let e=()=>{D(o())},s=()=>{P(t)&&(console.log("Day changed detected, performing reset..."),O()),e()};e();let n=setInterval(()=>{s()},6e4);return()=>clearInterval(n)},[t]);let O=async()=>{x(!0);try{let e=P(t),s=JSON.parse(localStorage.getItem("routine_tracker_users")||"[]").find(e=>e.id===t);f(s);let n=I(t);if(e||!n||n.date!==d()){let e={userId:t,date:d(),completedItems:[],lastUpdated:c()};r(e),k(e)}else r(n);w(t)||setTimeout(()=>{B(!0)},1e3)}catch(e){console.error("Error initializing tracker:",e)}finally{x(!1)}},M=async e=>{if(a&&!g){h(!0);try{let t=a.completedItems.includes(e)?a.completedItems.filter(t=>t!==e):[...a.completedItems,e],s={...a,completedItems:t,lastUpdated:c()};r(s),k(s)}catch(e){console.error("Error updating progress:",e)}finally{h(!1)}}},W=e=>{E(e)};if((0,i.useEffect)(()=>{let e=e=>{b&&(e.target.closest(".relative")||j(!1))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[b]),l)return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 mb-2",children:"Loading your tracker..."}),(0,n.jsx)("p",{className:"text-sm text-indigo-600 font-semibold",children:"Built with ❤️ by Tech Talk"})]})});if(!a)return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-red-600",children:"Error loading your progress. Please try again."}),(0,n.jsx)("button",{onClick:O,className:"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700",children:"Retry"})]})});let q=u(a.completedItems,R.length),U=m();return(0,n.jsx)("div",{className:"min-h-screen p-4",children:(0,n.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-12 h-12 rounded-lg shadow-md"}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",(null==p?void 0:p.name)||"Friend","! \uD83D\uDC4B"]}),(0,n.jsx)("p",{className:"text-gray-600",children:U})]})]}),(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("button",{onClick:()=>{j(!b)},className:"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",title:"Menu",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})}),b&&(0,n.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10",children:[(0,n.jsxs)("button",{onClick:()=>{S(!0),j(!1)},className:"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2",children:[(0,n.jsx)("span",{children:"\uD83D\uDCCA"}),(0,n.jsx)("span",{children:"Weekly Report"})]}),(0,n.jsx)("div",{className:"border-t border-gray-100 my-1"}),(0,n.jsxs)("button",{onClick:()=>{y(""),s()},className:"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2",children:[(0,n.jsx)("span",{children:"\uD83D\uDC64"}),(0,n.jsx)("span",{children:"Switch User"})]})]})]})})]}),(0,n.jsxs)("div",{className:"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Todays Progress"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-indigo-600",children:[a.completedItems.length,"/",R.length]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Completion Rate"}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-indigo-600",children:[q,"%"]})]})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("div",{className:"bg-gray-200 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(q,"%")}})})})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-6",children:"Daily Routine Checklist ✅"}),(0,n.jsx)("div",{className:"space-y-3",children:R.map(e=>{let t=a.completedItems.includes(e.id);return(0,n.jsxs)("div",{className:"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ".concat(t?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50"),onClick:()=>M(e.id),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,n.jsx)("div",{className:"text-2xl",children:e.icon}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h3",{className:"font-medium ".concat(t?"text-green-800":"text-gray-800"),children:e.name}),(0,n.jsx)("p",{className:"text-sm ".concat(t?"text-green-600":"text-gray-600"),children:e.description})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[e.detailedInfo&&(0,n.jsx)("button",{onClick:t=>{t.stopPropagation(),W(e)},className:"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors",title:"More info",children:(0,n.jsx)("span",{className:"text-indigo-600 text-sm font-bold",children:"ⓘ"})}),(0,n.jsx)("div",{className:"w-6 h-6 rounded-full border-2 flex items-center justify-center ".concat(t?"border-green-500 bg-green-500":"border-gray-300"),children:t&&(0,n.jsx)("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})]},e.id)})}),g&&(0,n.jsx)("div",{className:"mt-4 text-center text-sm text-gray-600",children:(0,n.jsxs)("div",{className:"inline-flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"}),(0,n.jsx)("span",{children:"Saving..."})]})})]}),(0,n.jsxs)("div",{className:"text-center mt-6 text-gray-600",children:[(0,n.jsx)("p",{className:"text-sm",children:100===q?"\uD83C\uDF89 Amazing! Youve completed all your routines today!":q>=75?"\uD83D\uDD25 Youre doing great! Keep it up!":q>=50?"\uD83D\uDCAA Good progress! Youre halfway there!":"\uD83C\uDF31 Every step counts. Youve got this!"}),(0,n.jsx)("p",{className:"text-xs mt-2",children:"Progress auto-saves • Resets at midnight • Access Weekly Report from menu"}),(0,n.jsxs)("p",{className:"text-xs mt-2",children:["Built with ❤️ by ",(0,n.jsx)("span",{className:"font-semibold text-indigo-600",children:"Tech Talk"})]})]}),N&&(0,n.jsx)(L,{userId:t,onClose:()=>{S(!1)}}),C&&(0,n.jsx)(T,{item:C,onClose:()=>{E(null)}}),_&&p&&(0,n.jsx)(A,{userName:p.name,onSetPin:e=>{v(t,e),B(!1)},onSkip:()=>{B(!1)}})]})})}let B=[{name:"Obinna",displayName:"Mchost",role:"admin",joinDate:"2025-01-10"},{name:"Daniel",displayName:"Aj danny Roze",role:"user",joinDate:"2025-01-10"}],O=e=>{let t=e.toLowerCase().trim();return B.some(e=>e.name.toLowerCase()===t)},M=e=>{let t=e.toLowerCase().trim();return B.find(e=>e.name.toLowerCase()===t)||null},W=e=>{let t=M(e);return(null==t?void 0:t.displayName)||e};function q(e){let{onUserLogin:t}=e,[s,a]=(0,i.useState)(""),[r,l]=(0,i.useState)(!1),[o,d]=(0,i.useState)(""),m=async e=>{if(e.preventDefault(),s.trim()){l(!0),d("");try{if(!O(s.trim())){d("Access denied. You are not authorized to use this tracker."),l(!1);return}let e=W(s.trim()),n=f(s.trim())||f(e);n?(n.lastActive=c(),n.name=e):n={id:x(s.trim()),name:e,createdAt:c(),lastActive:c()},h(n),y(n.id),t(n.id)}catch(e){console.error("Error logging in:",e),d("An error occurred. Please try again.")}finally{l(!1)}}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"max-w-md w-full",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-20 h-20 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Digital Routine & Results Tracker"}),(0,n.jsx)("p",{className:"text-gray-600 text-lg",children:"Track your daily growth with intention"}),(0,n.jsx)("p",{className:"text-sm text-indigo-600 font-semibold mt-2",children:"Built with ❤️ by Tech Talk"})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"What were building here \uD83D\uDCBB✅"}),(0,n.jsxs)("div",{className:"space-y-3 text-gray-600",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Type in your name"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Tick what youve done for the day (Prayer, Study, Hygiene, Work, etc.)"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Submit, and it saves your progress"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"Come back anytime before the day ends to update it"})]}),(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsx)("span",{className:"text-indigo-500 mt-1",children:"•"}),(0,n.jsx)("span",{children:"System resets at midnight, but keeps a history of your growth"})]})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Ready to track your growth? \uD83D\uDCCA✨"}),(0,n.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name"}),(0,n.jsx)("input",{type:"text",id:"name",value:s,onChange:e=>a(e.target.value),placeholder:"Enter your name...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors",disabled:r,required:!0})]}),o&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,n.jsx)("p",{className:"text-red-600 text-sm",children:o})}),(0,n.jsx)("button",{type:"submit",disabled:r||!s.trim(),className:"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:r?(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,n.jsx)("span",{children:"Getting started..."})]}):"Start Tracking \uD83D\uDD25"})]})]}),(0,n.jsx)("div",{className:"text-center mt-6 text-gray-500 text-sm",children:(0,n.jsx)("p",{children:"No login stress. No judgment. Just you, your goals, and your growth."})})]})})}function U(e){let{userName:t,onPinSubmit:s,onBack:a,error:r}=e,[l,o]=(0,i.useState)(""),[d,c]=(0,i.useState)(!1);(0,i.useEffect)(()=>{4===l.length&&m()},[l]);let m=async()=>{if(4===l.length){c(!0);try{s(l)}catch(e){console.error("PIN submission error:",e)}finally{c(!1)}}},u=e=>{l.length<4&&o(t=>t+e)};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,n.jsxs)("div",{className:"max-w-md w-full",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:["Welcome back, ",t,"! \uD83D\uDC4B"]}),(0,n.jsx)("p",{className:"text-gray-600",children:"Enter your 4-digit PIN to continue"})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsx)("div",{className:"flex justify-center space-x-4 mb-6",children:[0,1,2,3].map(e=>(0,n.jsx)("div",{className:"w-12 h-12 rounded-lg border-2 flex items-center justify-center text-2xl font-bold ".concat(l.length>e?"border-indigo-500 bg-indigo-50 text-indigo-600":"border-gray-300 bg-gray-50"),children:l.length>e?"•":""},e))}),r&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,n.jsx)("p",{className:"text-red-600 text-sm text-center",children:r})}),(0,n.jsx)("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[1,2,3,4,5,6,7,8,9].map(e=>(0,n.jsx)("button",{onClick:()=>u(e.toString()),disabled:l.length>=4||d,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:e},e))}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-3 mb-6",children:[(0,n.jsx)("button",{onClick:()=>{o("")},disabled:0===l.length||d,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Clear"}),(0,n.jsx)("button",{onClick:()=>u("0"),disabled:l.length>=4||d,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"0"}),(0,n.jsx)("button",{onClick:()=>{o(e=>e.slice(0,-1))},disabled:0===l.length||d,className:"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"⌫"})]}),d&&(0,n.jsx)("div",{className:"text-center",children:(0,n.jsxs)("div",{className:"inline-flex items-center space-x-2 text-indigo-600",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"}),(0,n.jsx)("span",{className:"text-sm",children:"Verifying..."})]})})]}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("button",{onClick:a,disabled:d,className:"text-indigo-600 hover:text-indigo-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:"← Back to name entry"})}),(0,n.jsx)("div",{className:"text-center mt-6",children:(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Built with ❤️ by Tech Talk"})})]})})}function z(){let[e,t]=(0,i.useState)(null),[s,a]=(0,i.useState)(!0),[r,l]=(0,i.useState)(!1),[o,d]=(0,i.useState)(null),[c,m]=(0,i.useState)("");return((0,i.useEffect)(()=>{t(b()),a(!1)},[]),s)?(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("img",{src:"/icon.png",alt:"Routine Tracker",className:"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md"}),(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 mb-2",children:"Loading your routine tracker..."}),(0,n.jsx)("p",{className:"text-sm text-indigo-600 font-semibold",children:"Built with ❤️ by Tech Talk"})]})}):r&&o?(0,n.jsx)(U,{userName:(()=>{if(!o)return"";let e=p().find(e=>e.id===o);return(null==e?void 0:e.name)||""})(),onPinSubmit:e=>{o&&(N(o,e)?(t(o),l(!1),d(null),m("")):m("Incorrect PIN. Please try again."))},onBack:()=>{l(!1),d(null),m("")},error:c}):(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:e?(0,n.jsx)(_,{userId:e,onLogout:()=>t(null)}):(0,n.jsx)(q,{onUserLogin:e=>{w(e)?(d(e),l(!0),m("")):t(e)}})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(2128)),_N_E=e.O()}]);