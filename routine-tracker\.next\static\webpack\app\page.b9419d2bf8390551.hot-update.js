"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _WeeklyReport__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WeeklyReport */ \"(app-pages-browser)/./src/components/WeeklyReport.tsx\");\n/* harmony import */ var _TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TaskInfoPopup */ \"(app-pages-browser)/./src/components/TaskInfoPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RoutineTracker(param) {\n    let { userId, onLogout } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWeeklyReport, setShowWeeklyReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayRoutineItems, setTodayRoutineItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPinSetup, setShowPinSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    // Update routine items when day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const updateRoutineItems = {\n                \"RoutineTracker.useEffect.updateRoutineItems\": ()=>{\n                    setTodayRoutineItems((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n                }\n            }[\"RoutineTracker.useEffect.updateRoutineItems\"];\n            // Update immediately\n            updateRoutineItems();\n            // Set up interval to check for day change every minute\n            const interval = setInterval({\n                \"RoutineTracker.useEffect.interval\": ()=>{\n                    updateRoutineItems();\n                }\n            }[\"RoutineTracker.useEffect.interval\"], 60000); // Check every minute\n            return ({\n                \"RoutineTracker.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], []);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n            // Check if user needs PIN setup (only show once)\n            if (!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.userHasPin)(userId)) {\n                // Show PIN setup popup after a short delay\n                setTimeout(()=>{\n                    setShowPinSetup(true);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    const toggleMenu = ()=>{\n        setShowMenu(!showMenu);\n    };\n    const openWeeklyReport = ()=>{\n        setShowWeeklyReport(true);\n        setShowMenu(false);\n    };\n    const closeWeeklyReport = ()=>{\n        setShowWeeklyReport(false);\n    };\n    const handleSetPin = (pin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, pin);\n        setShowPinSetup(false);\n    };\n    const handleSkipPin = ()=>{\n        setShowPinSetup(false);\n    };\n    const openTaskInfo = (task)=>{\n        setSelectedTask(task);\n    };\n    const closeTaskInfo = ()=>{\n        setSelectedTask(null);\n    };\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"RoutineTracker.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.relative')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"RoutineTracker.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"RoutineTracker.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        showMenu\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, todayRoutineItems.length);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getNigerianTimeDisplay)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icon.png\",\n                                            alt: \"Routine Tracker\",\n                                            className: \"w-12 h-12 rounded-lg shadow-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.name) || 'Friend',\n                                                        \"! \\uD83D\\uDC4B\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: currentDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n                                                title: \"Menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openWeeklyReport,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCCA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Weekly Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC64\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Switch User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Todays Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        todayRoutineItems.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(completionRate, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayRoutineItems.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'),\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium \".concat(isCompleted ? 'text-green-800' : 'text-gray-800'),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.detailedInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openTaskInfo(item);\n                                                    },\n                                                    className: \"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors\",\n                                                    title: \"More info\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-indigo-600 text-sm font-bold\",\n                                                        children: \"ⓘ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center \".concat(isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'),\n                                                    children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! Youve completed all your routines today!\" : completionRate >= 75 ? \"🔥 Youre doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! Youre halfway there!\" : \"🌱 Every step counts. Youve got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: \"Progress auto-saves • Resets at midnight • Access Weekly Report from menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this),\n                showWeeklyReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyReport__WEBPACK_IMPORTED_MODULE_4__.WeeklyReport, {\n                    userId: userId,\n                    onClose: closeWeeklyReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this),\n                selectedTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__.TaskInfoPopup, {\n                    item: selectedTask,\n                    onClose: closeTaskInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutineTracker, \"6zdwML8RTS8jYarve9NpMnnmxzQ=\");\n_c = RoutineTracker;\nvar _c;\n$RefreshReg$(_c, \"RoutineTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RoutineTracker.tsx\n"));

/***/ })

});