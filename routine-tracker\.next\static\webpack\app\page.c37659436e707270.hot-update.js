"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RoutineTracker */ \"(app-pages-browser)/./src/components/RoutineTracker.tsx\");\n/* harmony import */ var _components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/WelcomeScreen */ \"(app-pages-browser)/./src/components/WelcomeScreen.tsx\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPinInput, setShowPinInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingUserId, setPendingUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pinError, setPinError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Check if user is already logged in\n            const userId = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.getCurrentUser)();\n            setCurrentUserId(userId);\n            setIsLoading(false);\n        }\n    }[\"Home.useEffect\"], []);\n    const handleUserLogin = (userId)=>{\n        // Check if user has PIN setup\n        if ((0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.userHasPin)(userId)) {\n            // Show PIN input screen\n            setPendingUserId(userId);\n            setShowPinInput(true);\n            setPinError('');\n        } else {\n            // Direct login (no PIN required)\n            setCurrentUserId(userId);\n        }\n    };\n    const handlePinSubmit = (pin)=>{\n        if (!pendingUserId) return;\n        if ((0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.verifyPin)(pendingUserId, pin)) {\n            // PIN correct, log in user\n            setCurrentUserId(pendingUserId);\n            setShowPinInput(false);\n            setPendingUserId(null);\n            setPinError('');\n        } else {\n            // PIN incorrect\n            setPinError('Incorrect PIN. Please try again.');\n        }\n    };\n    const handlePinBack = ()=>{\n        setShowPinInput(false);\n        setPendingUserId(null);\n        setPinError('');\n    };\n    const getPendingUserName = ()=>{\n        if (!pendingUserId) return '';\n        const users = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.getUsers)();\n        const user = users.find((u)=>u.id === pendingUserId);\n        return (user === null || user === void 0 ? void 0 : user.name) || '';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your routine tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: currentUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__.RoutineTracker, {\n            userId: currentUserId,\n            onLogout: ()=>setCurrentUserId(null)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 86,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__.WelcomeScreen, {\n            onUserLogin: handleUserLogin\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"9+AK3vPCwU2GpPzEex5n+4FjjyM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2lCO0FBQ0Y7QUFFdUI7QUFFbkUsU0FBU1E7O0lBQ3RCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdWLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUNXLFdBQVdDLGFBQWEsR0FBR1osK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDYSxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDZSxlQUFlQyxpQkFBaUIsR0FBR2hCLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUNpQixVQUFVQyxZQUFZLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUV6Q0MsZ0RBQVNBOzBCQUFDO1lBQ1IscUNBQXFDO1lBQ3JDLE1BQU1rQixTQUFTZiw4REFBY0E7WUFDN0JNLGlCQUFpQlM7WUFDakJQLGFBQWE7UUFDZjt5QkFBRyxFQUFFO0lBRUwsTUFBTVEsa0JBQWtCLENBQUNEO1FBQ3ZCLDhCQUE4QjtRQUM5QixJQUFJZCwwREFBVUEsQ0FBQ2MsU0FBUztZQUN0Qix3QkFBd0I7WUFDeEJILGlCQUFpQkc7WUFDakJMLGdCQUFnQjtZQUNoQkksWUFBWTtRQUNkLE9BQU87WUFDTCxpQ0FBaUM7WUFDakNSLGlCQUFpQlM7UUFDbkI7SUFDRjtJQUVBLE1BQU1FLGtCQUFrQixDQUFDQztRQUN2QixJQUFJLENBQUNQLGVBQWU7UUFFcEIsSUFBSVQseURBQVNBLENBQUNTLGVBQWVPLE1BQU07WUFDakMsMkJBQTJCO1lBQzNCWixpQkFBaUJLO1lBQ2pCRCxnQkFBZ0I7WUFDaEJFLGlCQUFpQjtZQUNqQkUsWUFBWTtRQUNkLE9BQU87WUFDTCxnQkFBZ0I7WUFDaEJBLFlBQVk7UUFDZDtJQUNGO0lBRUEsTUFBTUssZ0JBQWdCO1FBQ3BCVCxnQkFBZ0I7UUFDaEJFLGlCQUFpQjtRQUNqQkUsWUFBWTtJQUNkO0lBRUEsTUFBTU0scUJBQXFCO1FBQ3pCLElBQUksQ0FBQ1QsZUFBZSxPQUFPO1FBQzNCLE1BQU1VLFFBQVFsQix3REFBUUE7UUFDdEIsTUFBTW1CLE9BQU9ELE1BQU1FLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLZDtRQUN0QyxPQUFPVyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1JLElBQUksS0FBSTtJQUN2QjtJQUVBLElBQUluQixXQUFXO1FBQ2IscUJBQ0UsOERBQUNvQjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUNDQyxLQUFJO3dCQUNKQyxLQUFJO3dCQUNKSCxXQUFVOzs7Ozs7a0NBRVosOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNJO3dCQUFFSixXQUFVO2tDQUFxQjs7Ozs7O2tDQUNsQyw4REFBQ0k7d0JBQUVKLFdBQVU7a0NBQXdDOzs7Ozs7Ozs7Ozs7Ozs7OztJQU03RDtJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVO2tCQUNadkIsOEJBQ0MsOERBQUNQLHNFQUFjQTtZQUFDaUIsUUFBUVY7WUFBZTRCLFVBQVUsSUFBTTNCLGlCQUFpQjs7Ozs7aUNBRXhFLDhEQUFDUCxvRUFBYUE7WUFBQ21DLGFBQWFsQjs7Ozs7Ozs7Ozs7QUFJcEM7R0FuRndCWjtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXNrdG9wXFxhcmNoaXZlLTIwMjUtMDYtMDVUMTQyNzIwKzAyMDBcXFByYWN0aWNlXFxSciAxLjBcXHJvdXRpbmUtdHJhY2tlclxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFJvdXRpbmVUcmFja2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL1JvdXRpbmVUcmFja2VyJztcbmltcG9ydCB7IFdlbGNvbWVTY3JlZW4gfSBmcm9tICdAL2NvbXBvbmVudHMvV2VsY29tZVNjcmVlbic7XG5pbXBvcnQgeyBQaW5JbnB1dFNjcmVlbiB9IGZyb20gJ0AvY29tcG9uZW50cy9QaW5JbnB1dFNjcmVlbic7XG5pbXBvcnQgeyBnZXRDdXJyZW50VXNlciwgdXNlckhhc1BpbiwgdmVyaWZ5UGluLCBnZXRVc2VycyB9IGZyb20gJ0AvdXRpbHMvc3RvcmFnZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIGNvbnN0IFtjdXJyZW50VXNlcklkLCBzZXRDdXJyZW50VXNlcklkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzaG93UGluSW5wdXQsIHNldFNob3dQaW5JbnB1dF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtwZW5kaW5nVXNlcklkLCBzZXRQZW5kaW5nVXNlcklkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbcGluRXJyb3IsIHNldFBpbkVycm9yXSA9IHVzZVN0YXRlKCcnKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYWxyZWFkeSBsb2dnZWQgaW5cbiAgICBjb25zdCB1c2VySWQgPSBnZXRDdXJyZW50VXNlcigpO1xuICAgIHNldEN1cnJlbnRVc2VySWQodXNlcklkKTtcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaGFuZGxlVXNlckxvZ2luID0gKHVzZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgUElOIHNldHVwXG4gICAgaWYgKHVzZXJIYXNQaW4odXNlcklkKSkge1xuICAgICAgLy8gU2hvdyBQSU4gaW5wdXQgc2NyZWVuXG4gICAgICBzZXRQZW5kaW5nVXNlcklkKHVzZXJJZCk7XG4gICAgICBzZXRTaG93UGluSW5wdXQodHJ1ZSk7XG4gICAgICBzZXRQaW5FcnJvcignJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIERpcmVjdCBsb2dpbiAobm8gUElOIHJlcXVpcmVkKVxuICAgICAgc2V0Q3VycmVudFVzZXJJZCh1c2VySWQpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVQaW5TdWJtaXQgPSAocGluOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXBlbmRpbmdVc2VySWQpIHJldHVybjtcblxuICAgIGlmICh2ZXJpZnlQaW4ocGVuZGluZ1VzZXJJZCwgcGluKSkge1xuICAgICAgLy8gUElOIGNvcnJlY3QsIGxvZyBpbiB1c2VyXG4gICAgICBzZXRDdXJyZW50VXNlcklkKHBlbmRpbmdVc2VySWQpO1xuICAgICAgc2V0U2hvd1BpbklucHV0KGZhbHNlKTtcbiAgICAgIHNldFBlbmRpbmdVc2VySWQobnVsbCk7XG4gICAgICBzZXRQaW5FcnJvcignJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFBJTiBpbmNvcnJlY3RcbiAgICAgIHNldFBpbkVycm9yKCdJbmNvcnJlY3QgUElOLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVQaW5CYWNrID0gKCkgPT4ge1xuICAgIHNldFNob3dQaW5JbnB1dChmYWxzZSk7XG4gICAgc2V0UGVuZGluZ1VzZXJJZChudWxsKTtcbiAgICBzZXRQaW5FcnJvcignJyk7XG4gIH07XG5cbiAgY29uc3QgZ2V0UGVuZGluZ1VzZXJOYW1lID0gKCk6IHN0cmluZyA9PiB7XG4gICAgaWYgKCFwZW5kaW5nVXNlcklkKSByZXR1cm4gJyc7XG4gICAgY29uc3QgdXNlcnMgPSBnZXRVc2VycygpO1xuICAgIGNvbnN0IHVzZXIgPSB1c2Vycy5maW5kKHUgPT4gdS5pZCA9PT0gcGVuZGluZ1VzZXJJZCk7XG4gICAgcmV0dXJuIHVzZXI/Lm5hbWUgfHwgJyc7XG4gIH07XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGltZ1xuICAgICAgICAgICAgc3JjPVwiL2ljb24ucG5nXCJcbiAgICAgICAgICAgIGFsdD1cIlJvdXRpbmUgVHJhY2tlclwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byBtYi00IHJvdW5kZWQtbGcgc2hhZG93LW1kXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTJcIj5Mb2FkaW5nIHlvdXIgcm91dGluZSB0cmFja2VyLi4uPC9wPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1pbmRpZ28tNjAwIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgIEJ1aWx0IHdpdGgg4p2k77iPIGJ5IFRlY2ggVGFsa1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMFwiPlxuICAgICAge2N1cnJlbnRVc2VySWQgPyAoXG4gICAgICAgIDxSb3V0aW5lVHJhY2tlciB1c2VySWQ9e2N1cnJlbnRVc2VySWR9IG9uTG9nb3V0PXsoKSA9PiBzZXRDdXJyZW50VXNlcklkKG51bGwpfSAvPlxuICAgICAgKSA6IChcbiAgICAgICAgPFdlbGNvbWVTY3JlZW4gb25Vc2VyTG9naW49e2hhbmRsZVVzZXJMb2dpbn0gLz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJSb3V0aW5lVHJhY2tlciIsIldlbGNvbWVTY3JlZW4iLCJnZXRDdXJyZW50VXNlciIsInVzZXJIYXNQaW4iLCJ2ZXJpZnlQaW4iLCJnZXRVc2VycyIsIkhvbWUiLCJjdXJyZW50VXNlcklkIiwic2V0Q3VycmVudFVzZXJJZCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInNob3dQaW5JbnB1dCIsInNldFNob3dQaW5JbnB1dCIsInBlbmRpbmdVc2VySWQiLCJzZXRQZW5kaW5nVXNlcklkIiwicGluRXJyb3IiLCJzZXRQaW5FcnJvciIsInVzZXJJZCIsImhhbmRsZVVzZXJMb2dpbiIsImhhbmRsZVBpblN1Ym1pdCIsInBpbiIsImhhbmRsZVBpbkJhY2siLCJnZXRQZW5kaW5nVXNlck5hbWUiLCJ1c2VycyIsInVzZXIiLCJmaW5kIiwidSIsImlkIiwibmFtZSIsImRpdiIsImNsYXNzTmFtZSIsImltZyIsInNyYyIsImFsdCIsInAiLCJvbkxvZ291dCIsIm9uVXNlckxvZ2luIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});