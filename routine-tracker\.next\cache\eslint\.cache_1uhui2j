[{"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx": "3", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx": "4", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx": "5", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx": "6", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts": "7", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts": "9", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts": "10"}, {"size": 689, "mtime": 1752249449047, "results": "11", "hashOfConfig": "12"}, {"size": 1641, "mtime": 1752191244991, "results": "13", "hashOfConfig": "12"}, {"size": 13547, "mtime": 1752249446720, "results": "14", "hashOfConfig": "12"}, {"size": 4791, "mtime": 1752158286771, "results": "15", "hashOfConfig": "12"}, {"size": 10123, "mtime": 1752166006347, "results": "16", "hashOfConfig": "12"}, {"size": 6115, "mtime": 1752249449225, "results": "17", "hashOfConfig": "12"}, {"size": 1918, "mtime": 1752157934473, "results": "18", "hashOfConfig": "12"}, {"size": 11610, "mtime": 1752249449892, "results": "19", "hashOfConfig": "12"}, {"size": 5172, "mtime": 1752187602092, "results": "20", "hashOfConfig": "12"}, {"size": 5411, "mtime": 1752186034713, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9cy87n", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx", ["52"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx", ["53", "54", "55"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx", ["56", "57"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx", ["58", "59"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx", ["60"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts", [], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 27, "column": 11, "nodeType": "63", "endLine": 31, "endColumn": 13}, {"ruleId": "64", "severity": 1, "message": "65", "line": 39, "column": 6, "nodeType": "66", "endLine": 39, "endColumn": 14, "suggestions": "67"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 165, "column": 11, "nodeType": "63", "endLine": 169, "endColumn": 13}, {"ruleId": "61", "severity": 1, "message": "62", "line": 206, "column": 15, "nodeType": "63", "endLine": 210, "endColumn": 17}, {"ruleId": "64", "severity": 1, "message": "68", "line": 31, "column": 6, "nodeType": "66", "endLine": 31, "endColumn": 8, "suggestions": "69"}, {"ruleId": "64", "severity": 1, "message": "68", "line": 46, "column": 6, "nodeType": "66", "endLine": 46, "endColumn": 8, "suggestions": "70"}, {"ruleId": "64", "severity": 1, "message": "71", "line": 26, "column": 6, "nodeType": "66", "endLine": 26, "endColumn": 28, "suggestions": "72"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 115, "column": 13, "nodeType": "63", "endLine": 119, "endColumn": 15}, {"ruleId": "61", "severity": 1, "message": "62", "line": 70, "column": 11, "nodeType": "63", "endLine": 74, "endColumn": 13}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeTracker'. Either include it or remove the dependency array.", "ArrayExpression", ["73"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["74"], ["75"], "React Hook useEffect has a missing dependency: 'loadWeeklyData'. Either include it or remove the dependency array.", ["76"], {"desc": "77", "fix": "78"}, {"desc": "79", "fix": "80"}, {"desc": "79", "fix": "81"}, {"desc": "82", "fix": "83"}, "Update the dependencies array to be: [initializeTracker, userId]", {"range": "84", "text": "85"}, "Update the dependencies array to be: [handleClose]", {"range": "86", "text": "87"}, {"range": "88", "text": "87"}, "Update the dependencies array to be: [userId, selectedWeek, loadWeeklyData]", {"range": "89", "text": "90"}, [1195, 1203], "[initializeTracker, userId]", [816, 818], "[handleClose]", [1150, 1152], [729, 751], "[userId, selectedWeek, loadWeeklyData]"]