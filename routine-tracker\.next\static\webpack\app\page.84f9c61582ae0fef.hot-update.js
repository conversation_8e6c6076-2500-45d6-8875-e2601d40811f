"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   getNigerianTimeDisplay: () => (/* binding */ getNigerianTimeDisplay),\n/* harmony export */   getRoutineItemsForDay: () => (/* binding */ getRoutineItemsForDay),\n/* harmony export */   getTodayRoutineItems: () => (/* binding */ getTodayRoutineItems),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection',\n        detailedInfo: {\n            purpose: 'Connect with the divine, find inner peace, and seek guidance for daily life.',\n            benefits: [\n                'Reduces stress and anxiety',\n                'Provides spiritual guidance and clarity',\n                'Strengthens faith and spiritual connection',\n                'Promotes gratitude and mindfulness',\n                'Offers comfort during difficult times'\n            ],\n            tips: [\n                'Set aside a quiet, dedicated space for prayer',\n                'Choose a consistent time each day',\n                'Start with gratitude and thanksgiving',\n                'Include prayers for others, not just yourself',\n                'Listen for guidance and inner peace'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development',\n        detailedInfo: {\n            purpose: 'Expand knowledge, develop skills, and pursue personal or professional growth through dedicated learning.',\n            benefits: [\n                'Improves cognitive function and memory',\n                'Enhances career prospects and opportunities',\n                'Builds confidence and self-esteem',\n                'Keeps mind sharp and engaged',\n                'Opens new perspectives and ideas'\n            ],\n            tips: [\n                'Create a distraction-free study environment',\n                'Use active learning techniques (notes, summaries)',\n                'Take regular breaks to maintain focus',\n                'Set specific, achievable learning goals',\n                'Review and practice regularly for retention'\n            ],\n            timeRecommendation: '30-120 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness',\n        detailedInfo: {\n            purpose: 'Maintain personal cleanliness, health, and confidence through proper hygiene practices.',\n            benefits: [\n                'Prevents illness and infections',\n                'Boosts self-confidence and social acceptance',\n                'Improves overall health and well-being',\n                'Creates positive first impressions',\n                'Reduces stress and anxiety about appearance'\n            ],\n            tips: [\n                'Establish a consistent daily routine',\n                'Use quality hygiene products suited for your skin type',\n                'Pay attention to often-missed areas (behind ears, between toes)',\n                'Replace hygiene items regularly (toothbrush, razors)',\n                'Stay hydrated to support healthy skin and hair'\n            ],\n            timeRecommendation: '20-45 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities',\n        detailedInfo: {\n            purpose: 'Accomplish professional goals, contribute value, and advance career through focused work.',\n            benefits: [\n                'Provides financial stability and security',\n                'Builds professional skills and experience',\n                'Creates sense of purpose and achievement',\n                'Develops problem-solving abilities',\n                'Expands professional network and opportunities'\n            ],\n            tips: [\n                'Set clear daily and weekly goals',\n                'Prioritize tasks using time management techniques',\n                'Take regular breaks to maintain productivity',\n                'Minimize distractions during focused work time',\n                'Continuously learn and improve your skills'\n            ],\n            timeRecommendation: '6-8 hours',\n            frequency: 'Weekdays'\n        }\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness',\n        detailedInfo: {\n            purpose: 'Maintain physical health, build strength, and improve overall well-being through regular exercise.',\n            benefits: [\n                'Improves cardiovascular health and endurance',\n                'Builds muscle strength and bone density',\n                'Enhances mental health and reduces stress',\n                'Boosts energy levels and sleep quality',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Start with activities you enjoy to build consistency',\n                'Gradually increase intensity and duration',\n                'Include both cardio and strength training',\n                'Stay hydrated before, during, and after exercise',\n                'Listen to your body and allow for rest days'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily or 5-6 times per week'\n        }\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning',\n        detailedInfo: {\n            purpose: 'Fuel your body with nutritious foods to support optimal health, energy, and well-being.',\n            benefits: [\n                'Provides essential nutrients for body functions',\n                'Maintains stable energy levels throughout the day',\n                'Supports immune system and disease prevention',\n                'Improves mental clarity and cognitive function',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Plan meals in advance to avoid unhealthy choices',\n                'Include a variety of colorful fruits and vegetables',\n                'Stay hydrated with plenty of water',\n                'Practice portion control and mindful eating',\n                'Limit processed foods and added sugars'\n            ],\n            timeRecommendation: '30-60 minutes for meal prep',\n            frequency: 'Daily meal planning and preparation'\n        }\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection',\n        detailedInfo: {\n            purpose: 'Gain self-awareness, process experiences, and promote personal growth through thoughtful reflection.',\n            benefits: [\n                'Increases self-awareness and emotional intelligence',\n                'Helps process and learn from experiences',\n                'Reduces stress and promotes mental clarity',\n                'Identifies patterns and areas for improvement',\n                'Enhances gratitude and positive mindset'\n            ],\n            tips: [\n                'Set aside quiet time without distractions',\n                'Write freely without worrying about grammar',\n                'Ask yourself meaningful questions about your day',\n                'Focus on both challenges and achievements',\n                'Review past entries to track growth and patterns'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily, preferably evening'\n        }\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions',\n        detailedInfo: {\n            purpose: 'Build and maintain meaningful relationships that provide support, joy, and personal growth.',\n            benefits: [\n                'Reduces feelings of loneliness and isolation',\n                'Provides emotional support and encouragement',\n                'Enhances mental health and well-being',\n                'Creates opportunities for learning and growth',\n                'Builds a strong support network for life challenges'\n            ],\n            tips: [\n                'Be present and actively listen during conversations',\n                'Reach out to friends and family regularly',\n                'Engage in shared activities and interests',\n                'Show genuine interest in others\\' lives and experiences',\n                'Practice empathy and offer support when needed'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily interactions, deeper connections weekly'\n        }\n    },\n    {\n        id: 'fasting',\n        name: 'Fasting',\n        icon: '🌙',\n        description: 'Spiritual fasting practice',\n        daysOfWeek: [\n            3,\n            5\n        ],\n        detailedInfo: {\n            purpose: 'Engage in spiritual discipline, self-control, and deeper connection with faith through fasting.',\n            benefits: [\n                'Develops self-discipline and willpower',\n                'Enhances spiritual awareness and focus',\n                'Promotes gratitude for daily blessings',\n                'Encourages prayer and meditation',\n                'Builds empathy for those less fortunate'\n            ],\n            tips: [\n                'Start with shorter fasting periods if new to fasting',\n                'Stay hydrated with water throughout the day',\n                'Use fasting time for prayer and reflection',\n                'Break your fast gently with light, healthy foods',\n                'Consult healthcare provider if you have medical conditions'\n            ],\n            timeRecommendation: 'Sunrise to sunset',\n            frequency: 'Wednesdays and Fridays'\n        }\n    }\n];\n// Get Nigerian time (West Africa Time - UTC+1)\nconst getNigerianTime = ()=>{\n    // Create a date in Nigerian timezone\n    const now = new Date();\n    const nigerianTimeString = now.toLocaleString('en-US', {\n        timeZone: 'Africa/Lagos'\n    });\n    return new Date(nigerianTimeString);\n};\n// Get routine items for a specific day (filters by day of week)\nconst getRoutineItemsForDay = (date)=>{\n    const targetDate = date || getNigerianTime();\n    const dayOfWeek = targetDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday\n    return DEFAULT_ROUTINE_ITEMS.filter((item)=>{\n        // If no specific days are defined, show the item every day\n        if (!item.daysOfWeek || item.daysOfWeek.length === 0) {\n            return true;\n        }\n        // Otherwise, only show on specified days\n        return item.daysOfWeek.includes(dayOfWeek);\n    });\n};\n// Get routine items for today\nconst getTodayRoutineItems = ()=>{\n    return getRoutineItemsForDay();\n};\n// Utility functions for date handling (using Nigerian time)\nconst getCurrentDate = ()=>{\n    return getNigerianTime().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return getNigerianTime().toISOString();\n};\n// Get current Nigerian time for display\nconst getNigerianTimeDisplay = ()=>{\n    return new Date().toLocaleDateString('en-US', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        timeZone: 'Africa/Lagos'\n    });\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (consistent based on name only)\nconst generateUserId = (name)=>{\n    // Create a consistent ID based on the name only (no timestamp)\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    // Add a simple hash to make it more unique but still consistent\n    let hash = 0;\n    for(let i = 0; i < name.length; i++){\n        const char = name.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return \"\".concat(nameHash, \"-\").concat(Math.abs(hash));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ })

});