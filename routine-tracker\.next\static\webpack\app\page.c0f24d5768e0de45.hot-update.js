"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   getNigerianTimeDisplay: () => (/* binding */ getNigerianTimeDisplay),\n/* harmony export */   getRoutineItemsForDay: () => (/* binding */ getRoutineItemsForDay),\n/* harmony export */   getTodayRoutineItems: () => (/* binding */ getTodayRoutineItems),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection',\n        detailedInfo: {\n            purpose: 'Connect with the divine, find inner peace, and seek guidance for daily life.',\n            benefits: [\n                'Reduces stress and anxiety',\n                'Provides spiritual guidance and clarity',\n                'Strengthens faith and spiritual connection',\n                'Promotes gratitude and mindfulness',\n                'Offers comfort during difficult times'\n            ],\n            tips: [\n                'Set aside a quiet, dedicated space for prayer',\n                'Choose a consistent time each day',\n                'Start with gratitude and thanksgiving',\n                'Include prayers for others, not just yourself',\n                'Listen for guidance and inner peace'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development',\n        detailedInfo: {\n            purpose: 'Expand knowledge, develop skills, and pursue personal or professional growth through dedicated learning.',\n            benefits: [\n                'Improves cognitive function and memory',\n                'Enhances career prospects and opportunities',\n                'Builds confidence and self-esteem',\n                'Keeps mind sharp and engaged',\n                'Opens new perspectives and ideas'\n            ],\n            tips: [\n                'Create a distraction-free study environment',\n                'Use active learning techniques (notes, summaries)',\n                'Take regular breaks to maintain focus',\n                'Set specific, achievable learning goals',\n                'Review and practice regularly for retention'\n            ],\n            timeRecommendation: '30-120 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness',\n        detailedInfo: {\n            purpose: 'Maintain personal cleanliness, health, and confidence through proper hygiene practices.',\n            benefits: [\n                'Prevents illness and infections',\n                'Boosts self-confidence and social acceptance',\n                'Improves overall health and well-being',\n                'Creates positive first impressions',\n                'Reduces stress and anxiety about appearance'\n            ],\n            tips: [\n                'Establish a consistent daily routine',\n                'Use quality hygiene products suited for your skin type',\n                'Pay attention to often-missed areas (behind ears, between toes)',\n                'Replace hygiene items regularly (toothbrush, razors)',\n                'Stay hydrated to support healthy skin and hair'\n            ],\n            timeRecommendation: '20-45 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities',\n        detailedInfo: {\n            purpose: 'Accomplish professional goals, contribute value, and advance career through focused work.',\n            benefits: [\n                'Provides financial stability and security',\n                'Builds professional skills and experience',\n                'Creates sense of purpose and achievement',\n                'Develops problem-solving abilities',\n                'Expands professional network and opportunities'\n            ],\n            tips: [\n                'Set clear daily and weekly goals',\n                'Prioritize tasks using time management techniques',\n                'Take regular breaks to maintain productivity',\n                'Minimize distractions during focused work time',\n                'Continuously learn and improve your skills'\n            ],\n            timeRecommendation: '6-8 hours',\n            frequency: 'Weekdays'\n        }\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness',\n        detailedInfo: {\n            purpose: 'Maintain physical health, build strength, and improve overall well-being through regular exercise.',\n            benefits: [\n                'Improves cardiovascular health and endurance',\n                'Builds muscle strength and bone density',\n                'Enhances mental health and reduces stress',\n                'Boosts energy levels and sleep quality',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Start with activities you enjoy to build consistency',\n                'Gradually increase intensity and duration',\n                'Include both cardio and strength training',\n                'Stay hydrated before, during, and after exercise',\n                'Listen to your body and allow for rest days'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily or 5-6 times per week'\n        }\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning',\n        detailedInfo: {\n            purpose: 'Fuel your body with nutritious foods to support optimal health, energy, and well-being.',\n            benefits: [\n                'Provides essential nutrients for body functions',\n                'Maintains stable energy levels throughout the day',\n                'Supports immune system and disease prevention',\n                'Improves mental clarity and cognitive function',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Plan meals in advance to avoid unhealthy choices',\n                'Include a variety of colorful fruits and vegetables',\n                'Stay hydrated with plenty of water',\n                'Practice portion control and mindful eating',\n                'Limit processed foods and added sugars'\n            ],\n            timeRecommendation: '30-60 minutes for meal prep',\n            frequency: 'Daily meal planning and preparation'\n        }\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection',\n        detailedInfo: {\n            purpose: 'Gain self-awareness, process experiences, and promote personal growth through thoughtful reflection.',\n            benefits: [\n                'Increases self-awareness and emotional intelligence',\n                'Helps process and learn from experiences',\n                'Reduces stress and promotes mental clarity',\n                'Identifies patterns and areas for improvement',\n                'Enhances gratitude and positive mindset'\n            ],\n            tips: [\n                'Set aside quiet time without distractions',\n                'Write freely without worrying about grammar',\n                'Ask yourself meaningful questions about your day',\n                'Focus on both challenges and achievements',\n                'Review past entries to track growth and patterns'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily, preferably evening'\n        }\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions',\n        detailedInfo: {\n            purpose: 'Build and maintain meaningful relationships that provide support, joy, and personal growth.',\n            benefits: [\n                'Reduces feelings of loneliness and isolation',\n                'Provides emotional support and encouragement',\n                'Enhances mental health and well-being',\n                'Creates opportunities for learning and growth',\n                'Builds a strong support network for life challenges'\n            ],\n            tips: [\n                'Be present and actively listen during conversations',\n                'Reach out to friends and family regularly',\n                'Engage in shared activities and interests',\n                'Show genuine interest in others\\' lives and experiences',\n                'Practice empathy and offer support when needed'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily interactions, deeper connections weekly'\n        }\n    },\n    {\n        id: 'fasting',\n        name: 'Fasting',\n        icon: '🌙',\n        description: 'Spiritual fasting practice',\n        daysOfWeek: [\n            3,\n            5\n        ],\n        detailedInfo: {\n            purpose: 'Engage in spiritual discipline, self-control, and deeper connection with faith through fasting.',\n            benefits: [\n                'Develops self-discipline and willpower',\n                'Enhances spiritual awareness and focus',\n                'Promotes gratitude for daily blessings',\n                'Encourages prayer and meditation',\n                'Builds empathy for those less fortunate'\n            ],\n            tips: [\n                'Start with shorter fasting periods if new to fasting',\n                'Stay hydrated with water throughout the day',\n                'Use fasting time for prayer and reflection',\n                'Break your fast gently with light, healthy foods',\n                'Consult healthcare provider if you have medical conditions'\n            ],\n            timeRecommendation: 'Sunrise to sunset',\n            frequency: 'Wednesdays and Fridays'\n        }\n    }\n];\n// Get Nigerian time (West Africa Time - UTC+1)\nconst getNigerianTime = ()=>{\n    // Create a date in Nigerian timezone\n    const now = new Date();\n    const nigerianTimeString = now.toLocaleString('en-US', {\n        timeZone: 'Africa/Lagos'\n    });\n    return new Date(nigerianTimeString);\n};\n// Get routine items for a specific day (filters by day of week)\nconst getRoutineItemsForDay = (date)=>{\n    const targetDate = date || getNigerianTime();\n    const dayOfWeek = targetDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday\n    return DEFAULT_ROUTINE_ITEMS.filter((item)=>{\n        // If no specific days are defined, show the item every day\n        if (!item.daysOfWeek || item.daysOfWeek.length === 0) {\n            return true;\n        }\n        // Otherwise, only show on specified days\n        return item.daysOfWeek.includes(dayOfWeek);\n    });\n};\n// Get routine items for today\nconst getTodayRoutineItems = ()=>{\n    return getRoutineItemsForDay();\n};\n// Utility functions for date handling (using Nigerian time)\nconst getCurrentDate = ()=>{\n    return getNigerianTime().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return getNigerianTime().toISOString();\n};\n// Get current Nigerian time for display\nconst getNigerianTimeDisplay = ()=>{\n    return new Date().toLocaleDateString('en-US', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        timeZone: 'Africa/Lagos'\n    });\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (consistent based on name only)\nconst generateUserId = (name)=>{\n    // Create a consistent ID based on the name only (no timestamp)\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    // Add a simple hash to make it more unique but still consistent\n    let hash = 0;\n    for(let i = 0; i < name.length; i++){\n        const char = name.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return \"\".concat(nameHash, \"-\").concat(Math.abs(hash));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy90eXBlcy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw0REFBNEQ7QUF5QzVELDZDQUE2QztBQUN0QyxNQUFNQSx3QkFBdUM7SUFDbEQ7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0lBQ0E7UUFDRVQsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiTyxZQUFZO1lBQUM7WUFBRztTQUFFO1FBQ2xCTixjQUFjO1lBQ1pDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE1BQU07Z0JBQ0o7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxvQkFBb0I7WUFDcEJDLFdBQVc7UUFDYjtJQUNGO0NBQ0QsQ0FBQztBQUVGLCtDQUErQztBQUMvQyxNQUFNRSxrQkFBa0I7SUFDdEIscUNBQXFDO0lBQ3JDLE1BQU1DLE1BQU0sSUFBSUM7SUFDaEIsTUFBTUMscUJBQXFCRixJQUFJRyxjQUFjLENBQUMsU0FBUztRQUFFQyxVQUFVO0lBQWU7SUFDbEYsT0FBTyxJQUFJSCxLQUFLQztBQUNsQjtBQUVBLGdFQUFnRTtBQUN6RCxNQUFNRyx3QkFBd0IsQ0FBQ0M7SUFDcEMsTUFBTUMsYUFBYUQsUUFBUVA7SUFDM0IsTUFBTVMsWUFBWUQsV0FBV0UsTUFBTSxJQUFJLHNDQUFzQztJQUU3RSxPQUFPdEIsc0JBQXNCdUIsTUFBTSxDQUFDQyxDQUFBQTtRQUNsQywyREFBMkQ7UUFDM0QsSUFBSSxDQUFDQSxLQUFLYixVQUFVLElBQUlhLEtBQUtiLFVBQVUsQ0FBQ2MsTUFBTSxLQUFLLEdBQUc7WUFDcEQsT0FBTztRQUNUO1FBQ0EseUNBQXlDO1FBQ3pDLE9BQU9ELEtBQUtiLFVBQVUsQ0FBQ2UsUUFBUSxDQUFDTDtJQUNsQztBQUNGLEVBQUU7QUFFRiw4QkFBOEI7QUFDdkIsTUFBTU0sdUJBQXVCO0lBQ2xDLE9BQU9UO0FBQ1QsRUFBRTtBQUVGLDREQUE0RDtBQUNyRCxNQUFNVSxpQkFBaUI7SUFDNUIsT0FBT2hCLGtCQUFrQmlCLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO0FBQ3RELEVBQUU7QUFFSyxNQUFNQyxzQkFBc0I7SUFDakMsT0FBT25CLGtCQUFrQmlCLFdBQVc7QUFDdEMsRUFBRTtBQUVGLHdDQUF3QztBQUNqQyxNQUFNRyx5QkFBeUI7SUFDcEMsT0FBTyxJQUFJbEIsT0FBT21CLGtCQUFrQixDQUFDLFNBQVM7UUFDNUNDLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7UUFDTHBCLFVBQVU7SUFDWjtBQUNGLEVBQUU7QUFFRiw0Q0FBNEM7QUFDckMsTUFBTXFCLFdBQVcsQ0FBQ0M7SUFDdkIsT0FBT1gscUJBQXFCVztBQUM5QixFQUFFO0FBRUYsNEJBQTRCO0FBQ3JCLE1BQU1DLDBCQUEwQixDQUFDQyxXQUFxQkM7SUFDM0QsT0FBT0MsS0FBS0MsS0FBSyxDQUFDLFVBQVduQixNQUFNLEdBQUdpQixRQUFTO0FBQ2pELEVBQUU7QUFFRixtREFBbUQ7QUFDNUMsTUFBTUcsaUJBQWlCLENBQUMzQztJQUM3QiwrREFBK0Q7SUFDL0QsTUFBTTRDLFdBQVc1QyxLQUFLNkMsV0FBVyxHQUFHQyxPQUFPLENBQUMsUUFBUTtJQUNwRCxnRUFBZ0U7SUFDaEUsSUFBSUMsT0FBTztJQUNYLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJaEQsS0FBS3VCLE1BQU0sRUFBRXlCLElBQUs7UUFDcEMsTUFBTUMsT0FBT2pELEtBQUtrRCxVQUFVLENBQUNGO1FBQzdCRCxPQUFPLENBQUVBLFFBQVEsS0FBS0EsT0FBUUU7UUFDOUJGLE9BQU9BLE9BQU9BLE1BQU0sNEJBQTRCO0lBQ2xEO0lBQ0EsT0FBTyxHQUFlTixPQUFaRyxVQUFTLEtBQWtCLE9BQWZILEtBQUtVLEdBQUcsQ0FBQ0o7QUFDakMsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXNrdG9wXFxhcmNoaXZlLTIwMjUtMDYtMDVUMTQyNzIwKzAyMDBcXFByYWN0aWNlXFxSciAxLjBcXHJvdXRpbmUtdHJhY2tlclxcc3JjXFx0eXBlc1xcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29yZSBkYXRhIHR5cGVzIGZvciB0aGUgRGlnaXRhbCBSb3V0aW5lICYgUmVzdWx0cyBUcmFja2VyXG5cbmV4cG9ydCBpbnRlcmZhY2UgUm91dGluZUl0ZW0ge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGljb246IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGRheXNPZldlZWs/OiBudW1iZXJbXTsgLy8gT3B0aW9uYWw6IHNwZWNpZmljIGRheXMgKDA9U3VuZGF5LCAxPU1vbmRheSwgLi4uLCA2PVNhdHVyZGF5KS4gSWYgbm90IHNwZWNpZmllZCwgc2hvd3MgZXZlcnkgZGF5XG4gIGRldGFpbGVkSW5mbz86IHtcbiAgICBwdXJwb3NlOiBzdHJpbmc7XG4gICAgYmVuZWZpdHM6IHN0cmluZ1tdO1xuICAgIHRpcHM6IHN0cmluZ1tdO1xuICAgIHRpbWVSZWNvbW1lbmRhdGlvbj86IHN0cmluZztcbiAgICBmcmVxdWVuY3k/OiBzdHJpbmc7XG4gIH07XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRGFpbHlQcm9ncmVzcyB7XG4gIHVzZXJJZDogc3RyaW5nO1xuICBkYXRlOiBzdHJpbmc7IC8vIFlZWVktTU0tREQgZm9ybWF0XG4gIGNvbXBsZXRlZEl0ZW1zOiBzdHJpbmdbXTsgLy8gQXJyYXkgb2Ygcm91dGluZSBpdGVtIElEc1xuICBsYXN0VXBkYXRlZDogc3RyaW5nOyAvLyBJU08gdGltZXN0YW1wXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7IC8vIElTTyB0aW1lc3RhbXBcbiAgbGFzdEFjdGl2ZTogc3RyaW5nOyAvLyBJU08gdGltZXN0YW1wXG4gIHBpbj86IHN0cmluZzsgLy8gT3B0aW9uYWwgUElOIGZvciBzZWN1cml0eSAoaGFzaGVkKVxuICBoYXNQaW5TZXR1cD86IGJvb2xlYW47IC8vIFdoZXRoZXIgdXNlciBoYXMgc2V0IHVwIFBJTlxufVxuXG5leHBvcnQgaW50ZXJmYWNlIEhpc3RvcmljYWxEYXRhIHtcbiAgdXNlcklkOiBzdHJpbmc7XG4gIGRhdGU6IHN0cmluZzsgLy8gWVlZWS1NTS1ERCBmb3JtYXRcbiAgY29tcGxldGVkSXRlbXM6IHN0cmluZ1tdO1xuICBjb21wbGV0aW9uUmF0ZTogbnVtYmVyOyAvLyBQZXJjZW50YWdlICgwLTEwMClcbiAgc3RyZWFrOiBudW1iZXI7IC8vIERheXMgaW4gYSByb3cgd2l0aCBhY3Rpdml0eVxufVxuXG4vLyBEZWZhdWx0IHJvdXRpbmUgaXRlbXMgdGhhdCBldmVyeSB1c2VyIGdldHNcbmV4cG9ydCBjb25zdCBERUZBVUxUX1JPVVRJTkVfSVRFTVM6IFJvdXRpbmVJdGVtW10gPSBbXG4gIHtcbiAgICBpZDogJ3ByYXllcicsXG4gICAgbmFtZTogJ1ByYXllcicsXG4gICAgaWNvbjogJ/CfmY8nLFxuICAgIGRlc2NyaXB0aW9uOiAnRGFpbHkgc3Bpcml0dWFsIHByYWN0aWNlIGFuZCByZWZsZWN0aW9uJyxcbiAgICBkZXRhaWxlZEluZm86IHtcbiAgICAgIHB1cnBvc2U6ICdDb25uZWN0IHdpdGggdGhlIGRpdmluZSwgZmluZCBpbm5lciBwZWFjZSwgYW5kIHNlZWsgZ3VpZGFuY2UgZm9yIGRhaWx5IGxpZmUuJyxcbiAgICAgIGJlbmVmaXRzOiBbXG4gICAgICAgICdSZWR1Y2VzIHN0cmVzcyBhbmQgYW54aWV0eScsXG4gICAgICAgICdQcm92aWRlcyBzcGlyaXR1YWwgZ3VpZGFuY2UgYW5kIGNsYXJpdHknLFxuICAgICAgICAnU3RyZW5ndGhlbnMgZmFpdGggYW5kIHNwaXJpdHVhbCBjb25uZWN0aW9uJyxcbiAgICAgICAgJ1Byb21vdGVzIGdyYXRpdHVkZSBhbmQgbWluZGZ1bG5lc3MnLFxuICAgICAgICAnT2ZmZXJzIGNvbWZvcnQgZHVyaW5nIGRpZmZpY3VsdCB0aW1lcydcbiAgICAgIF0sXG4gICAgICB0aXBzOiBbXG4gICAgICAgICdTZXQgYXNpZGUgYSBxdWlldCwgZGVkaWNhdGVkIHNwYWNlIGZvciBwcmF5ZXInLFxuICAgICAgICAnQ2hvb3NlIGEgY29uc2lzdGVudCB0aW1lIGVhY2ggZGF5JyxcbiAgICAgICAgJ1N0YXJ0IHdpdGggZ3JhdGl0dWRlIGFuZCB0aGFua3NnaXZpbmcnLFxuICAgICAgICAnSW5jbHVkZSBwcmF5ZXJzIGZvciBvdGhlcnMsIG5vdCBqdXN0IHlvdXJzZWxmJyxcbiAgICAgICAgJ0xpc3RlbiBmb3IgZ3VpZGFuY2UgYW5kIGlubmVyIHBlYWNlJ1xuICAgICAgXSxcbiAgICAgIHRpbWVSZWNvbW1lbmRhdGlvbjogJzEwLTMwIG1pbnV0ZXMnLFxuICAgICAgZnJlcXVlbmN5OiAnRGFpbHknXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICdzdHVkeScsXG4gICAgbmFtZTogJ1N0dWR5JyxcbiAgICBpY29uOiAn8J+TmicsXG4gICAgZGVzY3JpcHRpb246ICdMZWFybmluZywgcmVhZGluZywgb3Igc2tpbGwgZGV2ZWxvcG1lbnQnLFxuICAgIGRldGFpbGVkSW5mbzoge1xuICAgICAgcHVycG9zZTogJ0V4cGFuZCBrbm93bGVkZ2UsIGRldmVsb3Agc2tpbGxzLCBhbmQgcHVyc3VlIHBlcnNvbmFsIG9yIHByb2Zlc3Npb25hbCBncm93dGggdGhyb3VnaCBkZWRpY2F0ZWQgbGVhcm5pbmcuJyxcbiAgICAgIGJlbmVmaXRzOiBbXG4gICAgICAgICdJbXByb3ZlcyBjb2duaXRpdmUgZnVuY3Rpb24gYW5kIG1lbW9yeScsXG4gICAgICAgICdFbmhhbmNlcyBjYXJlZXIgcHJvc3BlY3RzIGFuZCBvcHBvcnR1bml0aWVzJyxcbiAgICAgICAgJ0J1aWxkcyBjb25maWRlbmNlIGFuZCBzZWxmLWVzdGVlbScsXG4gICAgICAgICdLZWVwcyBtaW5kIHNoYXJwIGFuZCBlbmdhZ2VkJyxcbiAgICAgICAgJ09wZW5zIG5ldyBwZXJzcGVjdGl2ZXMgYW5kIGlkZWFzJ1xuICAgICAgXSxcbiAgICAgIHRpcHM6IFtcbiAgICAgICAgJ0NyZWF0ZSBhIGRpc3RyYWN0aW9uLWZyZWUgc3R1ZHkgZW52aXJvbm1lbnQnLFxuICAgICAgICAnVXNlIGFjdGl2ZSBsZWFybmluZyB0ZWNobmlxdWVzIChub3Rlcywgc3VtbWFyaWVzKScsXG4gICAgICAgICdUYWtlIHJlZ3VsYXIgYnJlYWtzIHRvIG1haW50YWluIGZvY3VzJyxcbiAgICAgICAgJ1NldCBzcGVjaWZpYywgYWNoaWV2YWJsZSBsZWFybmluZyBnb2FscycsXG4gICAgICAgICdSZXZpZXcgYW5kIHByYWN0aWNlIHJlZ3VsYXJseSBmb3IgcmV0ZW50aW9uJ1xuICAgICAgXSxcbiAgICAgIHRpbWVSZWNvbW1lbmRhdGlvbjogJzMwLTEyMCBtaW51dGVzJyxcbiAgICAgIGZyZXF1ZW5jeTogJ0RhaWx5J1xuICAgIH1cbiAgfSxcbiAge1xuICAgIGlkOiAnaHlnaWVuZScsXG4gICAgbmFtZTogJ0h5Z2llbmUnLFxuICAgIGljb246ICfwn6e8JyxcbiAgICBkZXNjcmlwdGlvbjogJ1BlcnNvbmFsIGNhcmUgYW5kIGNsZWFubGluZXNzJyxcbiAgICBkZXRhaWxlZEluZm86IHtcbiAgICAgIHB1cnBvc2U6ICdNYWludGFpbiBwZXJzb25hbCBjbGVhbmxpbmVzcywgaGVhbHRoLCBhbmQgY29uZmlkZW5jZSB0aHJvdWdoIHByb3BlciBoeWdpZW5lIHByYWN0aWNlcy4nLFxuICAgICAgYmVuZWZpdHM6IFtcbiAgICAgICAgJ1ByZXZlbnRzIGlsbG5lc3MgYW5kIGluZmVjdGlvbnMnLFxuICAgICAgICAnQm9vc3RzIHNlbGYtY29uZmlkZW5jZSBhbmQgc29jaWFsIGFjY2VwdGFuY2UnLFxuICAgICAgICAnSW1wcm92ZXMgb3ZlcmFsbCBoZWFsdGggYW5kIHdlbGwtYmVpbmcnLFxuICAgICAgICAnQ3JlYXRlcyBwb3NpdGl2ZSBmaXJzdCBpbXByZXNzaW9ucycsXG4gICAgICAgICdSZWR1Y2VzIHN0cmVzcyBhbmQgYW54aWV0eSBhYm91dCBhcHBlYXJhbmNlJ1xuICAgICAgXSxcbiAgICAgIHRpcHM6IFtcbiAgICAgICAgJ0VzdGFibGlzaCBhIGNvbnNpc3RlbnQgZGFpbHkgcm91dGluZScsXG4gICAgICAgICdVc2UgcXVhbGl0eSBoeWdpZW5lIHByb2R1Y3RzIHN1aXRlZCBmb3IgeW91ciBza2luIHR5cGUnLFxuICAgICAgICAnUGF5IGF0dGVudGlvbiB0byBvZnRlbi1taXNzZWQgYXJlYXMgKGJlaGluZCBlYXJzLCBiZXR3ZWVuIHRvZXMpJyxcbiAgICAgICAgJ1JlcGxhY2UgaHlnaWVuZSBpdGVtcyByZWd1bGFybHkgKHRvb3RoYnJ1c2gsIHJhem9ycyknLFxuICAgICAgICAnU3RheSBoeWRyYXRlZCB0byBzdXBwb3J0IGhlYWx0aHkgc2tpbiBhbmQgaGFpcidcbiAgICAgIF0sXG4gICAgICB0aW1lUmVjb21tZW5kYXRpb246ICcyMC00NSBtaW51dGVzJyxcbiAgICAgIGZyZXF1ZW5jeTogJ0RhaWx5J1xuICAgIH1cbiAgfSxcbiAge1xuICAgIGlkOiAnd29yaycsXG4gICAgbmFtZTogJ1dvcmsnLFxuICAgIGljb246ICfwn5K8JyxcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCB0YXNrcyBhbmQgcmVzcG9uc2liaWxpdGllcycsXG4gICAgZGV0YWlsZWRJbmZvOiB7XG4gICAgICBwdXJwb3NlOiAnQWNjb21wbGlzaCBwcm9mZXNzaW9uYWwgZ29hbHMsIGNvbnRyaWJ1dGUgdmFsdWUsIGFuZCBhZHZhbmNlIGNhcmVlciB0aHJvdWdoIGZvY3VzZWQgd29yay4nLFxuICAgICAgYmVuZWZpdHM6IFtcbiAgICAgICAgJ1Byb3ZpZGVzIGZpbmFuY2lhbCBzdGFiaWxpdHkgYW5kIHNlY3VyaXR5JyxcbiAgICAgICAgJ0J1aWxkcyBwcm9mZXNzaW9uYWwgc2tpbGxzIGFuZCBleHBlcmllbmNlJyxcbiAgICAgICAgJ0NyZWF0ZXMgc2Vuc2Ugb2YgcHVycG9zZSBhbmQgYWNoaWV2ZW1lbnQnLFxuICAgICAgICAnRGV2ZWxvcHMgcHJvYmxlbS1zb2x2aW5nIGFiaWxpdGllcycsXG4gICAgICAgICdFeHBhbmRzIHByb2Zlc3Npb25hbCBuZXR3b3JrIGFuZCBvcHBvcnR1bml0aWVzJ1xuICAgICAgXSxcbiAgICAgIHRpcHM6IFtcbiAgICAgICAgJ1NldCBjbGVhciBkYWlseSBhbmQgd2Vla2x5IGdvYWxzJyxcbiAgICAgICAgJ1ByaW9yaXRpemUgdGFza3MgdXNpbmcgdGltZSBtYW5hZ2VtZW50IHRlY2huaXF1ZXMnLFxuICAgICAgICAnVGFrZSByZWd1bGFyIGJyZWFrcyB0byBtYWludGFpbiBwcm9kdWN0aXZpdHknLFxuICAgICAgICAnTWluaW1pemUgZGlzdHJhY3Rpb25zIGR1cmluZyBmb2N1c2VkIHdvcmsgdGltZScsXG4gICAgICAgICdDb250aW51b3VzbHkgbGVhcm4gYW5kIGltcHJvdmUgeW91ciBza2lsbHMnXG4gICAgICBdLFxuICAgICAgdGltZVJlY29tbWVuZGF0aW9uOiAnNi04IGhvdXJzJyxcbiAgICAgIGZyZXF1ZW5jeTogJ1dlZWtkYXlzJ1xuICAgIH1cbiAgfSxcbiAge1xuICAgIGlkOiAnZXhlcmNpc2UnLFxuICAgIG5hbWU6ICdFeGVyY2lzZScsXG4gICAgaWNvbjogJ/CfkqonLFxuICAgIGRlc2NyaXB0aW9uOiAnUGh5c2ljYWwgYWN0aXZpdHkgYW5kIGZpdG5lc3MnLFxuICAgIGRldGFpbGVkSW5mbzoge1xuICAgICAgcHVycG9zZTogJ01haW50YWluIHBoeXNpY2FsIGhlYWx0aCwgYnVpbGQgc3RyZW5ndGgsIGFuZCBpbXByb3ZlIG92ZXJhbGwgd2VsbC1iZWluZyB0aHJvdWdoIHJlZ3VsYXIgZXhlcmNpc2UuJyxcbiAgICAgIGJlbmVmaXRzOiBbXG4gICAgICAgICdJbXByb3ZlcyBjYXJkaW92YXNjdWxhciBoZWFsdGggYW5kIGVuZHVyYW5jZScsXG4gICAgICAgICdCdWlsZHMgbXVzY2xlIHN0cmVuZ3RoIGFuZCBib25lIGRlbnNpdHknLFxuICAgICAgICAnRW5oYW5jZXMgbWVudGFsIGhlYWx0aCBhbmQgcmVkdWNlcyBzdHJlc3MnLFxuICAgICAgICAnQm9vc3RzIGVuZXJneSBsZXZlbHMgYW5kIHNsZWVwIHF1YWxpdHknLFxuICAgICAgICAnSGVscHMgbWFpbnRhaW4gaGVhbHRoeSB3ZWlnaHQgYW5kIG1ldGFib2xpc20nXG4gICAgICBdLFxuICAgICAgdGlwczogW1xuICAgICAgICAnU3RhcnQgd2l0aCBhY3Rpdml0aWVzIHlvdSBlbmpveSB0byBidWlsZCBjb25zaXN0ZW5jeScsXG4gICAgICAgICdHcmFkdWFsbHkgaW5jcmVhc2UgaW50ZW5zaXR5IGFuZCBkdXJhdGlvbicsXG4gICAgICAgICdJbmNsdWRlIGJvdGggY2FyZGlvIGFuZCBzdHJlbmd0aCB0cmFpbmluZycsXG4gICAgICAgICdTdGF5IGh5ZHJhdGVkIGJlZm9yZSwgZHVyaW5nLCBhbmQgYWZ0ZXIgZXhlcmNpc2UnLFxuICAgICAgICAnTGlzdGVuIHRvIHlvdXIgYm9keSBhbmQgYWxsb3cgZm9yIHJlc3QgZGF5cydcbiAgICAgIF0sXG4gICAgICB0aW1lUmVjb21tZW5kYXRpb246ICczMC02MCBtaW51dGVzJyxcbiAgICAgIGZyZXF1ZW5jeTogJ0RhaWx5IG9yIDUtNiB0aW1lcyBwZXIgd2VlaydcbiAgICB9XG4gIH0sXG4gIHtcbiAgICBpZDogJ251dHJpdGlvbicsXG4gICAgbmFtZTogJ051dHJpdGlvbicsXG4gICAgaWNvbjogJ/CfpZcnLFxuICAgIGRlc2NyaXB0aW9uOiAnSGVhbHRoeSBlYXRpbmcgYW5kIG1lYWwgcGxhbm5pbmcnLFxuICAgIGRldGFpbGVkSW5mbzoge1xuICAgICAgcHVycG9zZTogJ0Z1ZWwgeW91ciBib2R5IHdpdGggbnV0cml0aW91cyBmb29kcyB0byBzdXBwb3J0IG9wdGltYWwgaGVhbHRoLCBlbmVyZ3ksIGFuZCB3ZWxsLWJlaW5nLicsXG4gICAgICBiZW5lZml0czogW1xuICAgICAgICAnUHJvdmlkZXMgZXNzZW50aWFsIG51dHJpZW50cyBmb3IgYm9keSBmdW5jdGlvbnMnLFxuICAgICAgICAnTWFpbnRhaW5zIHN0YWJsZSBlbmVyZ3kgbGV2ZWxzIHRocm91Z2hvdXQgdGhlIGRheScsXG4gICAgICAgICdTdXBwb3J0cyBpbW11bmUgc3lzdGVtIGFuZCBkaXNlYXNlIHByZXZlbnRpb24nLFxuICAgICAgICAnSW1wcm92ZXMgbWVudGFsIGNsYXJpdHkgYW5kIGNvZ25pdGl2ZSBmdW5jdGlvbicsXG4gICAgICAgICdIZWxwcyBtYWludGFpbiBoZWFsdGh5IHdlaWdodCBhbmQgbWV0YWJvbGlzbSdcbiAgICAgIF0sXG4gICAgICB0aXBzOiBbXG4gICAgICAgICdQbGFuIG1lYWxzIGluIGFkdmFuY2UgdG8gYXZvaWQgdW5oZWFsdGh5IGNob2ljZXMnLFxuICAgICAgICAnSW5jbHVkZSBhIHZhcmlldHkgb2YgY29sb3JmdWwgZnJ1aXRzIGFuZCB2ZWdldGFibGVzJyxcbiAgICAgICAgJ1N0YXkgaHlkcmF0ZWQgd2l0aCBwbGVudHkgb2Ygd2F0ZXInLFxuICAgICAgICAnUHJhY3RpY2UgcG9ydGlvbiBjb250cm9sIGFuZCBtaW5kZnVsIGVhdGluZycsXG4gICAgICAgICdMaW1pdCBwcm9jZXNzZWQgZm9vZHMgYW5kIGFkZGVkIHN1Z2FycydcbiAgICAgIF0sXG4gICAgICB0aW1lUmVjb21tZW5kYXRpb246ICczMC02MCBtaW51dGVzIGZvciBtZWFsIHByZXAnLFxuICAgICAgZnJlcXVlbmN5OiAnRGFpbHkgbWVhbCBwbGFubmluZyBhbmQgcHJlcGFyYXRpb24nXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICdyZWZsZWN0aW9uJyxcbiAgICBuYW1lOiAnUmVmbGVjdGlvbicsXG4gICAgaWNvbjogJ/CfpJQnLFxuICAgIGRlc2NyaXB0aW9uOiAnRGFpbHkgam91cm5hbGluZyBvciBzZWxmLXJlZmxlY3Rpb24nLFxuICAgIGRldGFpbGVkSW5mbzoge1xuICAgICAgcHVycG9zZTogJ0dhaW4gc2VsZi1hd2FyZW5lc3MsIHByb2Nlc3MgZXhwZXJpZW5jZXMsIGFuZCBwcm9tb3RlIHBlcnNvbmFsIGdyb3d0aCB0aHJvdWdoIHRob3VnaHRmdWwgcmVmbGVjdGlvbi4nLFxuICAgICAgYmVuZWZpdHM6IFtcbiAgICAgICAgJ0luY3JlYXNlcyBzZWxmLWF3YXJlbmVzcyBhbmQgZW1vdGlvbmFsIGludGVsbGlnZW5jZScsXG4gICAgICAgICdIZWxwcyBwcm9jZXNzIGFuZCBsZWFybiBmcm9tIGV4cGVyaWVuY2VzJyxcbiAgICAgICAgJ1JlZHVjZXMgc3RyZXNzIGFuZCBwcm9tb3RlcyBtZW50YWwgY2xhcml0eScsXG4gICAgICAgICdJZGVudGlmaWVzIHBhdHRlcm5zIGFuZCBhcmVhcyBmb3IgaW1wcm92ZW1lbnQnLFxuICAgICAgICAnRW5oYW5jZXMgZ3JhdGl0dWRlIGFuZCBwb3NpdGl2ZSBtaW5kc2V0J1xuICAgICAgXSxcbiAgICAgIHRpcHM6IFtcbiAgICAgICAgJ1NldCBhc2lkZSBxdWlldCB0aW1lIHdpdGhvdXQgZGlzdHJhY3Rpb25zJyxcbiAgICAgICAgJ1dyaXRlIGZyZWVseSB3aXRob3V0IHdvcnJ5aW5nIGFib3V0IGdyYW1tYXInLFxuICAgICAgICAnQXNrIHlvdXJzZWxmIG1lYW5pbmdmdWwgcXVlc3Rpb25zIGFib3V0IHlvdXIgZGF5JyxcbiAgICAgICAgJ0ZvY3VzIG9uIGJvdGggY2hhbGxlbmdlcyBhbmQgYWNoaWV2ZW1lbnRzJyxcbiAgICAgICAgJ1JldmlldyBwYXN0IGVudHJpZXMgdG8gdHJhY2sgZ3Jvd3RoIGFuZCBwYXR0ZXJucydcbiAgICAgIF0sXG4gICAgICB0aW1lUmVjb21tZW5kYXRpb246ICcxMC0zMCBtaW51dGVzJyxcbiAgICAgIGZyZXF1ZW5jeTogJ0RhaWx5LCBwcmVmZXJhYmx5IGV2ZW5pbmcnXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICdjb25uZWN0aW9uJyxcbiAgICBuYW1lOiAnQ29ubmVjdGlvbicsXG4gICAgaWNvbjogJ/CfkaUnLFxuICAgIGRlc2NyaXB0aW9uOiAnTWVhbmluZ2Z1bCBzb2NpYWwgaW50ZXJhY3Rpb25zJyxcbiAgICBkZXRhaWxlZEluZm86IHtcbiAgICAgIHB1cnBvc2U6ICdCdWlsZCBhbmQgbWFpbnRhaW4gbWVhbmluZ2Z1bCByZWxhdGlvbnNoaXBzIHRoYXQgcHJvdmlkZSBzdXBwb3J0LCBqb3ksIGFuZCBwZXJzb25hbCBncm93dGguJyxcbiAgICAgIGJlbmVmaXRzOiBbXG4gICAgICAgICdSZWR1Y2VzIGZlZWxpbmdzIG9mIGxvbmVsaW5lc3MgYW5kIGlzb2xhdGlvbicsXG4gICAgICAgICdQcm92aWRlcyBlbW90aW9uYWwgc3VwcG9ydCBhbmQgZW5jb3VyYWdlbWVudCcsXG4gICAgICAgICdFbmhhbmNlcyBtZW50YWwgaGVhbHRoIGFuZCB3ZWxsLWJlaW5nJyxcbiAgICAgICAgJ0NyZWF0ZXMgb3Bwb3J0dW5pdGllcyBmb3IgbGVhcm5pbmcgYW5kIGdyb3d0aCcsXG4gICAgICAgICdCdWlsZHMgYSBzdHJvbmcgc3VwcG9ydCBuZXR3b3JrIGZvciBsaWZlIGNoYWxsZW5nZXMnXG4gICAgICBdLFxuICAgICAgdGlwczogW1xuICAgICAgICAnQmUgcHJlc2VudCBhbmQgYWN0aXZlbHkgbGlzdGVuIGR1cmluZyBjb252ZXJzYXRpb25zJyxcbiAgICAgICAgJ1JlYWNoIG91dCB0byBmcmllbmRzIGFuZCBmYW1pbHkgcmVndWxhcmx5JyxcbiAgICAgICAgJ0VuZ2FnZSBpbiBzaGFyZWQgYWN0aXZpdGllcyBhbmQgaW50ZXJlc3RzJyxcbiAgICAgICAgJ1Nob3cgZ2VudWluZSBpbnRlcmVzdCBpbiBvdGhlcnNcXCcgbGl2ZXMgYW5kIGV4cGVyaWVuY2VzJyxcbiAgICAgICAgJ1ByYWN0aWNlIGVtcGF0aHkgYW5kIG9mZmVyIHN1cHBvcnQgd2hlbiBuZWVkZWQnXG4gICAgICBdLFxuICAgICAgdGltZVJlY29tbWVuZGF0aW9uOiAnMzAtNjAgbWludXRlcycsXG4gICAgICBmcmVxdWVuY3k6ICdEYWlseSBpbnRlcmFjdGlvbnMsIGRlZXBlciBjb25uZWN0aW9ucyB3ZWVrbHknXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICdmYXN0aW5nJyxcbiAgICBuYW1lOiAnRmFzdGluZycsXG4gICAgaWNvbjogJ/CfjJknLFxuICAgIGRlc2NyaXB0aW9uOiAnU3Bpcml0dWFsIGZhc3RpbmcgcHJhY3RpY2UnLFxuICAgIGRheXNPZldlZWs6IFszLCA1XSwgLy8gV2VkbmVzZGF5ICgzKSBhbmQgRnJpZGF5ICg1KVxuICAgIGRldGFpbGVkSW5mbzoge1xuICAgICAgcHVycG9zZTogJ0VuZ2FnZSBpbiBzcGlyaXR1YWwgZGlzY2lwbGluZSwgc2VsZi1jb250cm9sLCBhbmQgZGVlcGVyIGNvbm5lY3Rpb24gd2l0aCBmYWl0aCB0aHJvdWdoIGZhc3RpbmcuJyxcbiAgICAgIGJlbmVmaXRzOiBbXG4gICAgICAgICdEZXZlbG9wcyBzZWxmLWRpc2NpcGxpbmUgYW5kIHdpbGxwb3dlcicsXG4gICAgICAgICdFbmhhbmNlcyBzcGlyaXR1YWwgYXdhcmVuZXNzIGFuZCBmb2N1cycsXG4gICAgICAgICdQcm9tb3RlcyBncmF0aXR1ZGUgZm9yIGRhaWx5IGJsZXNzaW5ncycsXG4gICAgICAgICdFbmNvdXJhZ2VzIHByYXllciBhbmQgbWVkaXRhdGlvbicsXG4gICAgICAgICdCdWlsZHMgZW1wYXRoeSBmb3IgdGhvc2UgbGVzcyBmb3J0dW5hdGUnXG4gICAgICBdLFxuICAgICAgdGlwczogW1xuICAgICAgICAnU3RhcnQgd2l0aCBzaG9ydGVyIGZhc3RpbmcgcGVyaW9kcyBpZiBuZXcgdG8gZmFzdGluZycsXG4gICAgICAgICdTdGF5IGh5ZHJhdGVkIHdpdGggd2F0ZXIgdGhyb3VnaG91dCB0aGUgZGF5JyxcbiAgICAgICAgJ1VzZSBmYXN0aW5nIHRpbWUgZm9yIHByYXllciBhbmQgcmVmbGVjdGlvbicsXG4gICAgICAgICdCcmVhayB5b3VyIGZhc3QgZ2VudGx5IHdpdGggbGlnaHQsIGhlYWx0aHkgZm9vZHMnLFxuICAgICAgICAnQ29uc3VsdCBoZWFsdGhjYXJlIHByb3ZpZGVyIGlmIHlvdSBoYXZlIG1lZGljYWwgY29uZGl0aW9ucydcbiAgICAgIF0sXG4gICAgICB0aW1lUmVjb21tZW5kYXRpb246ICdTdW5yaXNlIHRvIHN1bnNldCcsXG4gICAgICBmcmVxdWVuY3k6ICdXZWRuZXNkYXlzIGFuZCBGcmlkYXlzJ1xuICAgIH1cbiAgfVxuXTtcblxuLy8gR2V0IE5pZ2VyaWFuIHRpbWUgKFdlc3QgQWZyaWNhIFRpbWUgLSBVVEMrMSlcbmNvbnN0IGdldE5pZ2VyaWFuVGltZSA9ICgpOiBEYXRlID0+IHtcbiAgLy8gQ3JlYXRlIGEgZGF0ZSBpbiBOaWdlcmlhbiB0aW1lem9uZVxuICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICBjb25zdCBuaWdlcmlhblRpbWVTdHJpbmcgPSBub3cudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywgeyB0aW1lWm9uZTogJ0FmcmljYS9MYWdvcycgfSk7XG4gIHJldHVybiBuZXcgRGF0ZShuaWdlcmlhblRpbWVTdHJpbmcpO1xufTtcblxuLy8gR2V0IHJvdXRpbmUgaXRlbXMgZm9yIGEgc3BlY2lmaWMgZGF5IChmaWx0ZXJzIGJ5IGRheSBvZiB3ZWVrKVxuZXhwb3J0IGNvbnN0IGdldFJvdXRpbmVJdGVtc0ZvckRheSA9IChkYXRlPzogRGF0ZSk6IFJvdXRpbmVJdGVtW10gPT4ge1xuICBjb25zdCB0YXJnZXREYXRlID0gZGF0ZSB8fCBnZXROaWdlcmlhblRpbWUoKTtcbiAgY29uc3QgZGF5T2ZXZWVrID0gdGFyZ2V0RGF0ZS5nZXREYXkoKTsgLy8gMD1TdW5kYXksIDE9TW9uZGF5LCAuLi4sIDY9U2F0dXJkYXlcblxuICByZXR1cm4gREVGQVVMVF9ST1VUSU5FX0lURU1TLmZpbHRlcihpdGVtID0+IHtcbiAgICAvLyBJZiBubyBzcGVjaWZpYyBkYXlzIGFyZSBkZWZpbmVkLCBzaG93IHRoZSBpdGVtIGV2ZXJ5IGRheVxuICAgIGlmICghaXRlbS5kYXlzT2ZXZWVrIHx8IGl0ZW0uZGF5c09mV2Vlay5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICAvLyBPdGhlcndpc2UsIG9ubHkgc2hvdyBvbiBzcGVjaWZpZWQgZGF5c1xuICAgIHJldHVybiBpdGVtLmRheXNPZldlZWsuaW5jbHVkZXMoZGF5T2ZXZWVrKTtcbiAgfSk7XG59O1xuXG4vLyBHZXQgcm91dGluZSBpdGVtcyBmb3IgdG9kYXlcbmV4cG9ydCBjb25zdCBnZXRUb2RheVJvdXRpbmVJdGVtcyA9ICgpOiBSb3V0aW5lSXRlbVtdID0+IHtcbiAgcmV0dXJuIGdldFJvdXRpbmVJdGVtc0ZvckRheSgpO1xufTtcblxuLy8gVXRpbGl0eSBmdW5jdGlvbnMgZm9yIGRhdGUgaGFuZGxpbmcgKHVzaW5nIE5pZ2VyaWFuIHRpbWUpXG5leHBvcnQgY29uc3QgZ2V0Q3VycmVudERhdGUgPSAoKTogc3RyaW5nID0+IHtcbiAgcmV0dXJuIGdldE5pZ2VyaWFuVGltZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50VGltZXN0YW1wID0gKCk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBnZXROaWdlcmlhblRpbWUoKS50b0lTT1N0cmluZygpO1xufTtcblxuLy8gR2V0IGN1cnJlbnQgTmlnZXJpYW4gdGltZSBmb3IgZGlzcGxheVxuZXhwb3J0IGNvbnN0IGdldE5pZ2VyaWFuVGltZURpc3BsYXkgPSAoKTogc3RyaW5nID0+IHtcbiAgcmV0dXJuIG5ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICB3ZWVrZGF5OiAnbG9uZycsXG4gICAgeWVhcjogJ251bWVyaWMnLFxuICAgIG1vbnRoOiAnbG9uZycsXG4gICAgZGF5OiAnbnVtZXJpYycsXG4gICAgdGltZVpvbmU6ICdBZnJpY2EvTGFnb3MnXG4gIH0pO1xufTtcblxuLy8gQ2hlY2sgaWYgaXQncyBhIG5ldyBkYXkgKGZvciByZXNldCBsb2dpYylcbmV4cG9ydCBjb25zdCBpc05ld0RheSA9IChsYXN0RGF0ZTogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gIHJldHVybiBnZXRDdXJyZW50RGF0ZSgpICE9PSBsYXN0RGF0ZTtcbn07XG5cbi8vIENhbGN1bGF0ZSBjb21wbGV0aW9uIHJhdGVcbmV4cG9ydCBjb25zdCBjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZSA9IChjb21wbGV0ZWQ6IHN0cmluZ1tdLCB0b3RhbDogbnVtYmVyKTogbnVtYmVyID0+IHtcbiAgcmV0dXJuIE1hdGgucm91bmQoKGNvbXBsZXRlZC5sZW5ndGggLyB0b3RhbCkgKiAxMDApO1xufTtcblxuLy8gR2VuZXJhdGUgdXNlciBJRCAoY29uc2lzdGVudCBiYXNlZCBvbiBuYW1lIG9ubHkpXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVVc2VySWQgPSAobmFtZTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgLy8gQ3JlYXRlIGEgY29uc2lzdGVudCBJRCBiYXNlZCBvbiB0aGUgbmFtZSBvbmx5IChubyB0aW1lc3RhbXApXG4gIGNvbnN0IG5hbWVIYXNoID0gbmFtZS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKTtcbiAgLy8gQWRkIGEgc2ltcGxlIGhhc2ggdG8gbWFrZSBpdCBtb3JlIHVuaXF1ZSBidXQgc3RpbGwgY29uc2lzdGVudFxuICBsZXQgaGFzaCA9IDA7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbmFtZS5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGNoYXIgPSBuYW1lLmNoYXJDb2RlQXQoaSk7XG4gICAgaGFzaCA9ICgoaGFzaCA8PCA1KSAtIGhhc2gpICsgY2hhcjtcbiAgICBoYXNoID0gaGFzaCAmIGhhc2g7IC8vIENvbnZlcnQgdG8gMzItYml0IGludGVnZXJcbiAgfVxuICByZXR1cm4gYCR7bmFtZUhhc2h9LSR7TWF0aC5hYnMoaGFzaCl9YDtcbn07XG4iXSwibmFtZXMiOlsiREVGQVVMVF9ST1VUSU5FX0lURU1TIiwiaWQiLCJuYW1lIiwiaWNvbiIsImRlc2NyaXB0aW9uIiwiZGV0YWlsZWRJbmZvIiwicHVycG9zZSIsImJlbmVmaXRzIiwidGlwcyIsInRpbWVSZWNvbW1lbmRhdGlvbiIsImZyZXF1ZW5jeSIsImRheXNPZldlZWsiLCJnZXROaWdlcmlhblRpbWUiLCJub3ciLCJEYXRlIiwibmlnZXJpYW5UaW1lU3RyaW5nIiwidG9Mb2NhbGVTdHJpbmciLCJ0aW1lWm9uZSIsImdldFJvdXRpbmVJdGVtc0ZvckRheSIsImRhdGUiLCJ0YXJnZXREYXRlIiwiZGF5T2ZXZWVrIiwiZ2V0RGF5IiwiZmlsdGVyIiwiaXRlbSIsImxlbmd0aCIsImluY2x1ZGVzIiwiZ2V0VG9kYXlSb3V0aW5lSXRlbXMiLCJnZXRDdXJyZW50RGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJnZXRDdXJyZW50VGltZXN0YW1wIiwiZ2V0TmlnZXJpYW5UaW1lRGlzcGxheSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsIndlZWtkYXkiLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJpc05ld0RheSIsImxhc3REYXRlIiwiY2FsY3VsYXRlQ29tcGxldGlvblJhdGUiLCJjb21wbGV0ZWQiLCJ0b3RhbCIsIk1hdGgiLCJyb3VuZCIsImdlbmVyYXRlVXNlcklkIiwibmFtZUhhc2giLCJ0b0xvd2VyQ2FzZSIsInJlcGxhY2UiLCJoYXNoIiwiaSIsImNoYXIiLCJjaGFyQ29kZUF0IiwiYWJzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ })

});