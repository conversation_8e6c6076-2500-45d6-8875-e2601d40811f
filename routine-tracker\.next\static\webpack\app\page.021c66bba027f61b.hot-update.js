"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _WeeklyReport__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WeeklyReport */ \"(app-pages-browser)/./src/components/WeeklyReport.tsx\");\n/* harmony import */ var _TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TaskInfoPopup */ \"(app-pages-browser)/./src/components/TaskInfoPopup.tsx\");\n/* harmony import */ var _PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PinSetupPopup */ \"(app-pages-browser)/./src/components/PinSetupPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RoutineTracker(param) {\n    let { userId, onLogout } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWeeklyReport, setShowWeeklyReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayRoutineItems, setTodayRoutineItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPinSetup, setShowPinSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChangePinPopup, setShowChangePinPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPinChangeSuccess, setShowPinChangeSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    // Update routine items when day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const updateRoutineItems = {\n                \"RoutineTracker.useEffect.updateRoutineItems\": ()=>{\n                    setTodayRoutineItems((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n                }\n            }[\"RoutineTracker.useEffect.updateRoutineItems\"];\n            // Update immediately\n            updateRoutineItems();\n            // Set up interval to check for day change every minute\n            const interval = setInterval({\n                \"RoutineTracker.useEffect.interval\": ()=>{\n                    updateRoutineItems();\n                }\n            }[\"RoutineTracker.useEffect.interval\"], 60000); // Check every minute\n            return ({\n                \"RoutineTracker.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], []);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n            // Check if user needs PIN setup (only show once)\n            if (!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.userHasPin)(userId)) {\n                // Show PIN setup popup after a short delay\n                setTimeout(()=>{\n                    setShowPinSetup(true);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    const toggleMenu = ()=>{\n        setShowMenu(!showMenu);\n    };\n    const openWeeklyReport = ()=>{\n        setShowWeeklyReport(true);\n        setShowMenu(false);\n    };\n    const closeWeeklyReport = ()=>{\n        setShowWeeklyReport(false);\n    };\n    const handleSetPin = (pin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, pin);\n        setShowPinSetup(false);\n    };\n    const handleSkipPin = ()=>{\n        setShowPinSetup(false);\n    };\n    const openChangePinPopup = ()=>{\n        setShowChangePinPopup(true);\n        setShowMenu(false);\n    };\n    const handleChangePin = (currentPin, newPin)=>{\n        const success = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.changePinForUser)(userId, currentPin, newPin);\n        if (success) {\n            setShowChangePinPopup(false);\n            setShowPinChangeSuccess(true);\n            // Hide success message after 3 seconds\n            setTimeout(()=>{\n                setShowPinChangeSuccess(false);\n            }, 3000);\n        }\n        return success;\n    };\n    const handleCancelChangePin = ()=>{\n        setShowChangePinPopup(false);\n    };\n    const openTaskInfo = (task)=>{\n        setSelectedTask(task);\n    };\n    const closeTaskInfo = ()=>{\n        setSelectedTask(null);\n    };\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"RoutineTracker.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.relative')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"RoutineTracker.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"RoutineTracker.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        showMenu\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, todayRoutineItems.length);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getNigerianTimeDisplay)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icon.png\",\n                                            alt: \"Routine Tracker\",\n                                            className: \"w-12 h-12 rounded-lg shadow-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.name) || 'Friend',\n                                                        \"! \\uD83D\\uDC4B\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: currentDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n                                                title: \"Menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openWeeklyReport,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCCA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Weekly Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openChangePinPopup,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDD10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Change PIN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC64\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Switch User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Todays Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        todayRoutineItems.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(completionRate, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayRoutineItems.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'),\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium \".concat(isCompleted ? 'text-green-800' : 'text-gray-800'),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.detailedInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openTaskInfo(item);\n                                                    },\n                                                    className: \"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors\",\n                                                    title: \"More info\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-indigo-600 text-sm font-bold\",\n                                                        children: \"ⓘ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center \".concat(isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'),\n                                                    children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! Youve completed all your routines today!\" : completionRate >= 75 ? \"🔥 Youre doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! Youre halfway there!\" : \"🌱 Every step counts. Youve got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: \"Progress auto-saves • Resets at midnight • Access Weekly Report from menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this),\n                showWeeklyReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyReport__WEBPACK_IMPORTED_MODULE_4__.WeeklyReport, {\n                    userId: userId,\n                    onClose: closeWeeklyReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this),\n                selectedTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__.TaskInfoPopup, {\n                    item: selectedTask,\n                    onClose: closeTaskInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 11\n                }, this),\n                showPinSetup && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__.PinSetupPopup, {\n                    userName: user.name,\n                    onSetPin: handleSetPin,\n                    onSkip: handleSkipPin\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutineTracker, \"H2NLRYFGZA9YzblPFb0mS0Bvzgw=\");\n_c = RoutineTracker;\nvar _c;\n$RefreshReg$(_c, \"RoutineTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RoutineTracker.tsx\n"));

/***/ })

});