/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VTRVIlNUMlNUNEZXNrdG9wJTVDJTVDYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCUyQjAyMDAlNUMlNUNQcmFjdGljZSU1QyU1Q1JyJTIwMS4wJTVDJTVDcm91dGluZS10cmFja2VyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNFUlxcXFxEZXNrdG9wXFxcXGFyY2hpdmUtMjAyNS0wNi0wNVQxNDI3MjArMDIwMFxcXFxQcmFjdGljZVxcXFxSciAxLjBcXFxccm91dGluZS10cmFja2VyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"447x559\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCI0NDd4NTU5XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8333935c1c67\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERlc2t0b3BcXGFyY2hpdmUtMjAyNS0wNi0wNVQxNDI3MjArMDIwMFxcUHJhY3RpY2VcXFJyIDEuMFxccm91dGluZS10cmFja2VyXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MzMzOTM1YzFjNjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDcmVhdGUgTmV4dCBBcHBcIixcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VTRVIlNUMlNUNEZXNrdG9wJTVDJTVDYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCUyQjAyMDAlNUMlNUNQcmFjdGljZSU1QyU1Q1JyJTIwMS4wJTVDJTVDcm91dGluZS10cmFja2VyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNFUlxcXFxEZXNrdG9wXFxcXGFyY2hpdmUtMjAyNS0wNi0wNVQxNDI3MjArMDIwMFxcXFxQcmFjdGljZVxcXFxSciAxLjBcXFxccm91dGluZS10cmFja2VyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RoutineTracker */ \"(ssr)/./src/components/RoutineTracker.tsx\");\n/* harmony import */ var _components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/WelcomeScreen */ \"(ssr)/./src/components/WelcomeScreen.tsx\");\n/* harmony import */ var _components_PinInputScreen__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PinInputScreen */ \"(ssr)/./src/components/PinInputScreen.tsx\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/storage */ \"(ssr)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPinInput, setShowPinInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingUserId, setPendingUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pinError, setPinError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Check if user is already logged in\n            const userId = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getCurrentUser)();\n            setCurrentUserId(userId);\n            setIsLoading(false);\n        }\n    }[\"Home.useEffect\"], []);\n    const handleUserLogin = (userId)=>{\n        // Check if user has PIN setup\n        if ((0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.userHasPin)(userId)) {\n            // Show PIN input screen\n            setPendingUserId(userId);\n            setShowPinInput(true);\n            setPinError('');\n        } else {\n            // Direct login (no PIN required)\n            setCurrentUserId(userId);\n        }\n    };\n    const handlePinSubmit = (pin)=>{\n        if (!pendingUserId) return;\n        if ((0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.verifyPin)(pendingUserId, pin)) {\n            // PIN correct, log in user\n            setCurrentUserId(pendingUserId);\n            setShowPinInput(false);\n            setPendingUserId(null);\n            setPinError('');\n        } else {\n            // PIN incorrect\n            setPinError('Incorrect PIN. Please try again.');\n        }\n    };\n    const handlePinBack = ()=>{\n        setShowPinInput(false);\n        setPendingUserId(null);\n        setPinError('');\n    };\n    const getPendingUserName = ()=>{\n        if (!pendingUserId) return '';\n        const users = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getUsers)();\n        const user = users.find((u)=>u.id === pendingUserId);\n        return user?.name || '';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your routine tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    // Show PIN input screen if needed\n    if (showPinInput && pendingUserId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PinInputScreen__WEBPACK_IMPORTED_MODULE_4__.PinInputScreen, {\n            userName: getPendingUserName(),\n            onPinSubmit: handlePinSubmit,\n            onBack: handlePinBack,\n            error: pinError\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: currentUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__.RoutineTracker, {\n            userId: currentUserId,\n            onLogout: ()=>setCurrentUserId(null)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__.WelcomeScreen, {\n            onUserLogin: handleUserLogin\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PinInputScreen.tsx":
/*!*******************************************!*\
  !*** ./src/components/PinInputScreen.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PinInputScreen: () => (/* binding */ PinInputScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PinInputScreen auto */ \n\nfunction PinInputScreen({ userName, onPinSubmit, onBack, error }) {\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Auto-submit when 4 digits are entered\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PinInputScreen.useEffect\": ()=>{\n            if (pin.length === 4) {\n                handleSubmit();\n            }\n        }\n    }[\"PinInputScreen.useEffect\"], [\n        pin\n    ]);\n    const handleSubmit = async ()=>{\n        if (pin.length !== 4) return;\n        setIsLoading(true);\n        try {\n            onPinSubmit(pin);\n        } catch (error) {\n            console.error('PIN submission error:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePinChange = (value)=>{\n        // Only allow numbers and limit to 4 digits\n        const numericValue = value.replace(/\\D/g, '').slice(0, 4);\n        setPin(numericValue);\n    };\n    const handleKeypadClick = (digit)=>{\n        if (pin.length < 4) {\n            setPin((prev)=>prev + digit);\n        }\n    };\n    const handleBackspace = ()=>{\n        setPin((prev)=>prev.slice(0, -1));\n    };\n    const handleClear = ()=>{\n        setPin('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/icon.png\",\n                            alt: \"Routine Tracker\",\n                            className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: [\n                                \"Welcome back, \",\n                                userName,\n                                \"! \\uD83D\\uDC4B\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Enter your 4-digit PIN to continue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4 mb-6\",\n                            children: [\n                                0,\n                                1,\n                                2,\n                                3\n                            ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 rounded-lg border-2 flex items-center justify-center text-2xl font-bold ${pin.length > index ? 'border-indigo-500 bg-indigo-50 text-indigo-600' : 'border-gray-300 bg-gray-50'}`,\n                                    children: pin.length > index ? '•' : ''\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm text-center\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-3 mb-4\",\n                            children: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5,\n                                6,\n                                7,\n                                8,\n                                9\n                            ].map((digit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleKeypadClick(digit.toString()),\n                                    disabled: pin.length >= 4 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: digit\n                                }, digit, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClear,\n                                    disabled: pin.length === 0 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"Clear\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleKeypadClick('0'),\n                                    disabled: pin.length >= 4 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackspace,\n                                    disabled: pin.length === 0 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"⌫\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 text-indigo-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Verifying...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        disabled: isLoading,\n                        className: \"text-indigo-600 hover:text-indigo-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"← Back to name entry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PinInputScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PinSetupPopup.tsx":
/*!******************************************!*\
  !*** ./src/components/PinSetupPopup.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PinSetupPopup: () => (/* binding */ PinSetupPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PinSetupPopup auto */ \n\nfunction PinSetupPopup({ onSetPin, onSkip, userName }) {\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPin, setConfirmPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (pin.length !== 4) {\n            setError('PIN must be exactly 4 digits');\n            return;\n        }\n        if (!/^\\d{4}$/.test(pin)) {\n            setError('PIN must contain only numbers');\n            return;\n        }\n        if (pin !== confirmPin) {\n            setError('PINs do not match');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            onSetPin(pin);\n        } catch (error) {\n            setError('Failed to set PIN. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83D\\uDD10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Secure Your Account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Hi \",\n                                userName,\n                                \"! Set up a 4-digit PIN to protect your progress from unauthorized access.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-blue-900 mb-2\",\n                            children: \"Why set a PIN?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Prevent others from accessing your account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Keep your progress private and secure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Quick and easy to enter (just 4 digits)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"pin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Enter 4-digit PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"pin\",\n                                    value: pin,\n                                    onChange: (e)=>setPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"confirmPin\",\n                                    value: confirmPin,\n                                    onChange: (e)=>setConfirmPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onSkip,\n                                    disabled: isLoading,\n                                    className: \"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"Skip for now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !pin || !confirmPin,\n                                    className: \"flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Setting up...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this) : 'Set PIN 🔒'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500 text-center mt-4\",\n                    children: \"You can change or remove your PIN anytime from the menu\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PinSetupPopup.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(ssr)/./src/utils/storage.ts\");\n/* harmony import */ var _WeeklyReport__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WeeklyReport */ \"(ssr)/./src/components/WeeklyReport.tsx\");\n/* harmony import */ var _TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TaskInfoPopup */ \"(ssr)/./src/components/TaskInfoPopup.tsx\");\n/* harmony import */ var _PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PinSetupPopup */ \"(ssr)/./src/components/PinSetupPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \n\n\n\n\n\n\nfunction RoutineTracker({ userId, onLogout }) {\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWeeklyReport, setShowWeeklyReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayRoutineItems, setTodayRoutineItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPinSetup, setShowPinSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    // Update routine items when day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const updateRoutineItems = {\n                \"RoutineTracker.useEffect.updateRoutineItems\": ()=>{\n                    setTodayRoutineItems((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n                }\n            }[\"RoutineTracker.useEffect.updateRoutineItems\"];\n            // Update immediately\n            updateRoutineItems();\n            // Set up interval to check for day change every minute\n            const interval = setInterval({\n                \"RoutineTracker.useEffect.interval\": ()=>{\n                    updateRoutineItems();\n                }\n            }[\"RoutineTracker.useEffect.interval\"], 60000); // Check every minute\n            return ({\n                \"RoutineTracker.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], []);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n            // Check if user needs PIN setup (only show once)\n            if (!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.userHasPin)(userId)) {\n                // Show PIN setup popup after a short delay\n                setTimeout(()=>{\n                    setShowPinSetup(true);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    const toggleMenu = ()=>{\n        setShowMenu(!showMenu);\n    };\n    const openWeeklyReport = ()=>{\n        setShowWeeklyReport(true);\n        setShowMenu(false);\n    };\n    const closeWeeklyReport = ()=>{\n        setShowWeeklyReport(false);\n    };\n    const handleSetPin = (pin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, pin);\n        setShowPinSetup(false);\n    };\n    const handleSkipPin = ()=>{\n        setShowPinSetup(false);\n    };\n    const openTaskInfo = (task)=>{\n        setSelectedTask(task);\n    };\n    const closeTaskInfo = ()=>{\n        setSelectedTask(null);\n    };\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"RoutineTracker.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.relative')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"RoutineTracker.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"RoutineTracker.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        showMenu\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, todayRoutineItems.length);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getNigerianTimeDisplay)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icon.png\",\n                                            alt: \"Routine Tracker\",\n                                            className: \"w-12 h-12 rounded-lg shadow-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user?.name || 'Friend',\n                                                        \"! \\uD83D\\uDC4B\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: currentDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n                                                title: \"Menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openWeeklyReport,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCCA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Weekly Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC64\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Switch User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Todays Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        todayRoutineItems.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: `${completionRate}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayRoutineItems.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'}`,\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: `font-medium ${isCompleted ? 'text-green-800' : 'text-gray-800'}`,\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: `text-sm ${isCompleted ? 'text-green-600' : 'text-gray-600'}`,\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.detailedInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openTaskInfo(item);\n                                                    },\n                                                    className: \"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors\",\n                                                    title: \"More info\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-indigo-600 text-sm font-bold\",\n                                                        children: \"ⓘ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-6 h-6 rounded-full border-2 flex items-center justify-center ${isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'}`,\n                                                    children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! Youve completed all your routines today!\" : completionRate >= 75 ? \"🔥 Youre doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! Youre halfway there!\" : \"🌱 Every step counts. Youve got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: \"Progress auto-saves • Resets at midnight • Access Weekly Report from menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this),\n                showWeeklyReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyReport__WEBPACK_IMPORTED_MODULE_4__.WeeklyReport, {\n                    userId: userId,\n                    onClose: closeWeeklyReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this),\n                selectedTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__.TaskInfoPopup, {\n                    item: selectedTask,\n                    onClose: closeTaskInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this),\n                showPinSetup && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__.PinSetupPopup, {\n                    userName: user.name,\n                    onSetPin: handleSetPin,\n                    onSkip: handleSkipPin\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RoutineTracker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TaskInfoPopup.tsx":
/*!******************************************!*\
  !*** ./src/components/TaskInfoPopup.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskInfoPopup: () => (/* binding */ TaskInfoPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TaskInfoPopup auto */ \n\nfunction TaskInfoPopup({ item, onClose }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Animation effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TaskInfoPopup.useEffect\": ()=>{\n            setIsVisible(true);\n            // Close when clicking outside\n            const handleClickOutside = {\n                \"TaskInfoPopup.useEffect.handleClickOutside\": (event)=>{\n                    if (popupRef.current && !popupRef.current.contains(event.target)) {\n                        handleClose();\n                    }\n                }\n            }[\"TaskInfoPopup.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"TaskInfoPopup.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"TaskInfoPopup.useEffect\"];\n        }\n    }[\"TaskInfoPopup.useEffect\"], []);\n    // Handle escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TaskInfoPopup.useEffect\": ()=>{\n            const handleEscKey = {\n                \"TaskInfoPopup.useEffect.handleEscKey\": (event)=>{\n                    if (event.key === 'Escape') {\n                        handleClose();\n                    }\n                }\n            }[\"TaskInfoPopup.useEffect.handleEscKey\"];\n            document.addEventListener('keydown', handleEscKey);\n            return ({\n                \"TaskInfoPopup.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscKey);\n                }\n            })[\"TaskInfoPopup.useEffect\"];\n        }\n    }[\"TaskInfoPopup.useEffect\"], []);\n    const handleClose = ()=>{\n        setIsVisible(false);\n        setTimeout(()=>{\n            onClose();\n        }, 300); // Match transition duration\n    };\n    if (!item.detailedInfo) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300\",\n        style: {\n            opacity: isVisible ? 1 : 0\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: popupRef,\n            className: `bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[70vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                    children: \"Purpose\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: item.detailedInfo.purpose\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                    children: \"Benefits\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc pl-5 space-y-1\",\n                                    children: item.detailedInfo.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-gray-600\",\n                                            children: benefit\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                    children: \"Tips\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc pl-5 space-y-1\",\n                                    children: item.detailedInfo.tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-gray-600\",\n                                            children: tip\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-indigo-50 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    item.detailedInfo.timeRecommendation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-indigo-800 mb-1\",\n                                                children: \"Recommended Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-indigo-700\",\n                                                children: item.detailedInfo.timeRecommendation\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.detailedInfo.frequency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-indigo-800 mb-1\",\n                                                children: \"Frequency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-indigo-700\",\n                                                children: item.detailedInfo.frequency\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 p-4 bg-gray-50 rounded-b-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClose,\n                        className: \"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors\",\n                        children: \"Got it\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TaskInfoPopup.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WeeklyReport.tsx":
/*!*****************************************!*\
  !*** ./src/components/WeeklyReport.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeeklyReport: () => (/* binding */ WeeklyReport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(ssr)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ WeeklyReport auto */ \n\n\n\nfunction WeeklyReport({ userId, onClose }) {\n    const [weeklyData, setWeeklyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0 = current week, 1 = last week, etc.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklyReport.useEffect\": ()=>{\n            loadWeeklyData();\n        }\n    }[\"WeeklyReport.useEffect\"], [\n        userId,\n        selectedWeek\n    ]);\n    const loadWeeklyData = ()=>{\n        setIsLoading(true);\n        const data = [];\n        // Get the start of the selected week (Sunday)\n        const today = new Date();\n        const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - currentDay - selectedWeek * 7);\n        // Generate 7 days of data\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startOfWeek);\n            date.setDate(startOfWeek.getDate() + i);\n            const dateStr = date.toISOString().split('T')[0];\n            const progress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserProgress)(userId, dateStr);\n            const completionRate = progress ? (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.length) : 0;\n            data.push({\n                date: dateStr,\n                dayName: date.toLocaleDateString('en-US', {\n                    weekday: 'short'\n                }),\n                progress,\n                completionRate\n            });\n        }\n        setWeeklyData(data);\n        setIsLoading(false);\n    };\n    const getWeekTitle = ()=>{\n        if (selectedWeek === 0) return 'This Week';\n        if (selectedWeek === 1) return 'Last Week';\n        return `${selectedWeek + 1} Weeks Ago`;\n    };\n    const getWeeklyAverage = ()=>{\n        const totalRate = weeklyData.reduce((sum, day)=>sum + day.completionRate, 0);\n        return Math.round(totalRate / weeklyData.length);\n    };\n    const getBestDay = ()=>{\n        return weeklyData.reduce((best, day)=>day.completionRate > best.completionRate ? day : best);\n    };\n    const getStreakDays = ()=>{\n        return weeklyData.filter((day)=>day.completionRate > 0).length;\n    };\n    const getTaskStats = ()=>{\n        const taskStats = _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.map((item)=>{\n            const completedDays = weeklyData.filter((day)=>day.progress?.completedItems.includes(item.id)).length;\n            return {\n                ...item,\n                completedDays,\n                percentage: Math.round(completedDays / 7 * 100)\n            };\n        });\n        return taskStats.sort((a, b)=>b.percentage - a.percentage);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading weekly report...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/icon.png\",\n                                    alt: \"Routine Tracker\",\n                                    className: \"w-10 h-10 rounded-lg shadow-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"\\uD83D\\uDCCA Weekly Report\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: getWeekTitle()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedWeek(selectedWeek + 1),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                            title: \"Previous week\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 min-w-[100px] text-center\",\n                                            children: getWeekTitle()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedWeek(Math.max(0, selectedWeek - 1)),\n                                            disabled: selectedWeek === 0,\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            title: \"Next week\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M9 5l7 7-7 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-indigo-50 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-indigo-600\",\n                                            children: [\n                                                getWeeklyAverage(),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-indigo-700\",\n                                            children: \"Weekly Average\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                getStreakDays(),\n                                                \"/7\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-green-700\",\n                                            children: \"Active Days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-50 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: [\n                                                getBestDay().completionRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-purple-700\",\n                                            children: [\n                                                \"Best Day (\",\n                                                getBestDay().dayName,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Daily Progress\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-7 gap-2\",\n                                    children: weeklyData.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 mb-2\",\n                                                    children: day.dayName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-200 rounded-lg h-24 flex items-end justify-center p-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-full rounded transition-all duration-300 ${day.completionRate >= 80 ? 'bg-green-500' : day.completionRate >= 60 ? 'bg-yellow-500' : day.completionRate >= 40 ? 'bg-orange-500' : day.completionRate > 0 ? 'bg-red-500' : 'bg-gray-300'}`,\n                                                        style: {\n                                                            height: `${Math.max(day.completionRate, 5)}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 mt-1\",\n                                                    children: [\n                                                        day.completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Task Performance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: getTaskStats().map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: task.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: task.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        task.completedDays,\n                                                                        \"/7 days completed\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `h-2 rounded-full transition-all duration-300 ${task.percentage >= 80 ? 'bg-green-500' : task.percentage >= 60 ? 'bg-yellow-500' : task.percentage >= 40 ? 'bg-orange-500' : task.percentage > 0 ? 'bg-red-500' : 'bg-gray-300'}`,\n                                                                style: {\n                                                                    width: `${task.percentage}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 w-12 text-right\",\n                                                            children: [\n                                                                task.percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 p-6 bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors mb-3\",\n                            children: \"Close Report\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-xs text-gray-500\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9XZWVrbHlSZXBvcnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTRDO0FBQzRDO0FBQ3RDO0FBYzNDLFNBQVNLLGFBQWEsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQXFCO0lBQ2pFLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHVCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQzdELE1BQU0sQ0FBQ1UsV0FBV0MsYUFBYSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNZLGNBQWNDLGdCQUFnQixHQUFHYiwrQ0FBUUEsQ0FBQyxJQUFJLHdDQUF3QztJQUU3RkMsZ0RBQVNBO2tDQUFDO1lBQ1JhO1FBQ0Y7aUNBQUc7UUFBQ1I7UUFBUU07S0FBYTtJQUV6QixNQUFNRSxpQkFBaUI7UUFDckJILGFBQWE7UUFDYixNQUFNSSxPQUFxQixFQUFFO1FBRTdCLDhDQUE4QztRQUM5QyxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLE1BQU1DLGFBQWFGLE1BQU1HLE1BQU0sSUFBSSwrQkFBK0I7UUFDbEUsTUFBTUMsY0FBYyxJQUFJSCxLQUFLRDtRQUM3QkksWUFBWUMsT0FBTyxDQUFDTCxNQUFNTSxPQUFPLEtBQUtKLGFBQWNOLGVBQWU7UUFFbkUsMEJBQTBCO1FBQzFCLElBQUssSUFBSVcsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7WUFDMUIsTUFBTUMsT0FBTyxJQUFJUCxLQUFLRztZQUN0QkksS0FBS0gsT0FBTyxDQUFDRCxZQUFZRSxPQUFPLEtBQUtDO1lBQ3JDLE1BQU1FLFVBQVVELEtBQUtFLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBRWhELE1BQU1DLFdBQVd4QiwrREFBZUEsQ0FBQ0UsUUFBUW1CO1lBQ3pDLE1BQU1JLGlCQUFpQkQsV0FDbkJ6QiwrREFBdUJBLENBQUN5QixTQUFTRSxjQUFjLEVBQUU1Qix5REFBcUJBLENBQUM2QixNQUFNLElBQzdFO1lBRUpoQixLQUFLaUIsSUFBSSxDQUFDO2dCQUNSUixNQUFNQztnQkFDTlEsU0FBU1QsS0FBS1Usa0JBQWtCLENBQUMsU0FBUztvQkFBRUMsU0FBUztnQkFBUTtnQkFDN0RQO2dCQUNBQztZQUNGO1FBQ0Y7UUFFQXBCLGNBQWNNO1FBQ2RKLGFBQWE7SUFDZjtJQUVBLE1BQU15QixlQUFlO1FBQ25CLElBQUl4QixpQkFBaUIsR0FBRyxPQUFPO1FBQy9CLElBQUlBLGlCQUFpQixHQUFHLE9BQU87UUFDL0IsT0FBTyxHQUFHQSxlQUFlLEVBQUUsVUFBVSxDQUFDO0lBQ3hDO0lBRUEsTUFBTXlCLG1CQUFtQjtRQUN2QixNQUFNQyxZQUFZOUIsV0FBVytCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxNQUFRRCxNQUFNQyxJQUFJWixjQUFjLEVBQUU7UUFDNUUsT0FBT2EsS0FBS0MsS0FBSyxDQUFDTCxZQUFZOUIsV0FBV3VCLE1BQU07SUFDakQ7SUFFQSxNQUFNYSxhQUFhO1FBQ2pCLE9BQU9wQyxXQUFXK0IsTUFBTSxDQUFDLENBQUNNLE1BQU1KLE1BQzlCQSxJQUFJWixjQUFjLEdBQUdnQixLQUFLaEIsY0FBYyxHQUFHWSxNQUFNSTtJQUVyRDtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQixPQUFPdEMsV0FBV3VDLE1BQU0sQ0FBQ04sQ0FBQUEsTUFBT0EsSUFBSVosY0FBYyxHQUFHLEdBQUdFLE1BQU07SUFDaEU7SUFFQSxNQUFNaUIsZUFBZTtRQUNuQixNQUFNQyxZQUFZL0MseURBQXFCQSxDQUFDZ0QsR0FBRyxDQUFDQyxDQUFBQTtZQUMxQyxNQUFNQyxnQkFBZ0I1QyxXQUFXdUMsTUFBTSxDQUFDTixDQUFBQSxNQUN0Q0EsSUFBSWIsUUFBUSxFQUFFRSxlQUFldUIsU0FBU0YsS0FBS0csRUFBRSxHQUM3Q3ZCLE1BQU07WUFFUixPQUFPO2dCQUNMLEdBQUdvQixJQUFJO2dCQUNQQztnQkFDQUcsWUFBWWIsS0FBS0MsS0FBSyxDQUFDLGdCQUFpQixJQUFLO1lBQy9DO1FBQ0Y7UUFFQSxPQUFPTSxVQUFVTyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRUgsVUFBVSxHQUFHRSxFQUFFRixVQUFVO0lBQzdEO0lBRUEsSUFBSTdDLFdBQVc7UUFDYixxQkFDRSw4REFBQ2lEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUkxQztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNFO29DQUNDQyxLQUFJO29DQUNKQyxLQUFJO29DQUNKSixXQUFVOzs7Ozs7OENBRVosOERBQUNEOztzREFDQyw4REFBQ007NENBQUdMLFdBQVU7c0RBQW1DOzs7Ozs7c0RBQ2pELDhEQUFDQzs0Q0FBRUQsV0FBVTtzREFBaUJ4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUdsQyw4REFBQ3VCOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FDQ0MsU0FBUyxJQUFNdEQsZ0JBQWdCRCxlQUFlOzRDQUM5Q2dELFdBQVU7NENBQ1ZRLE9BQU07c0RBRU4sNEVBQUNDO2dEQUFJVCxXQUFVO2dEQUFVVSxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUNqRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFZO29EQUFJQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUd6RSw4REFBQ0M7NENBQUtsQixXQUFVO3NEQUFtRHhCOzs7Ozs7c0RBQ25FLDhEQUFDOEI7NENBQ0NDLFNBQVMsSUFBTXRELGdCQUFnQjZCLEtBQUtxQyxHQUFHLENBQUMsR0FBR25FLGVBQWU7NENBQzFEb0UsVUFBVXBFLGlCQUFpQjs0Q0FDM0JnRCxXQUFVOzRDQUNWUSxPQUFNO3NEQUVOLDRFQUFDQztnREFBSVQsV0FBVTtnREFBVVUsTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDakUsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBWTtvREFBSUMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJM0UsOERBQUNYO29DQUNDQyxTQUFTNUQ7b0NBQ1RxRCxXQUFVOzhDQUVWLDRFQUFDUzt3Q0FBSVQsV0FBVTt3Q0FBVVUsTUFBSzt3Q0FBT0MsUUFBTzt3Q0FBZUMsU0FBUTtrREFDakUsNEVBQUNDOzRDQUFLQyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBWTs0Q0FBSUMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPN0UsOERBQUNsQjtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Z0RBQXNDdkI7Z0RBQW1COzs7Ozs7O3NEQUN4RSw4REFBQ3NCOzRDQUFJQyxXQUFVO3NEQUEwQjs7Ozs7Ozs7Ozs7OzhDQUUzQyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Z0RBQXFDZDtnREFBZ0I7Ozs7Ozs7c0RBQ3BFLDhEQUFDYTs0Q0FBSUMsV0FBVTtzREFBeUI7Ozs7Ozs7Ozs7Ozs4Q0FFMUMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7O2dEQUFzQ2hCLGFBQWFmLGNBQWM7Z0RBQUM7Ozs7Ozs7c0RBQ2pGLDhEQUFDOEI7NENBQUlDLFdBQVU7O2dEQUEwQjtnREFBV2hCLGFBQWFYLE9BQU87Z0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzdFLDhEQUFDMEI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDcUI7b0NBQUdyQixXQUFVOzhDQUEyQzs7Ozs7OzhDQUN6RCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1pwRCxXQUFXMEMsR0FBRyxDQUFDLENBQUNULEtBQUt5QyxzQkFDcEIsOERBQUN2Qjs0Q0FBZ0JDLFdBQVU7OzhEQUN6Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQThCbkIsSUFBSVIsT0FBTzs7Ozs7OzhEQUN4RCw4REFBQzBCO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFDQ0MsV0FBVyxDQUFDLDJDQUEyQyxFQUNyRG5CLElBQUlaLGNBQWMsSUFBSSxLQUFLLGlCQUMzQlksSUFBSVosY0FBYyxJQUFJLEtBQUssa0JBQzNCWSxJQUFJWixjQUFjLElBQUksS0FBSyxrQkFDM0JZLElBQUlaLGNBQWMsR0FBRyxJQUFJLGVBQWUsZUFDeEM7d0RBQ0ZzRCxPQUFPOzREQUFFQyxRQUFRLEdBQUcxQyxLQUFLcUMsR0FBRyxDQUFDdEMsSUFBSVosY0FBYyxFQUFFLEdBQUcsQ0FBQyxDQUFDO3dEQUFDOzs7Ozs7Ozs7Ozs4REFHM0QsOERBQUM4QjtvREFBSUMsV0FBVTs7d0RBQThCbkIsSUFBSVosY0FBYzt3REFBQzs7Ozs7Ozs7MkNBYnhEcUQ7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBb0JoQiw4REFBQ3ZCOzs4Q0FDQyw4REFBQ3NCO29DQUFHckIsV0FBVTs4Q0FBMkM7Ozs7Ozs4Q0FDekQsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaWixlQUFlRSxHQUFHLENBQUMsQ0FBQ21DLHFCQUNuQiw4REFBQzFCOzRDQUFrQkMsV0FBVTs7OERBQzNCLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNrQjs0REFBS2xCLFdBQVU7c0VBQVl5QixLQUFLQyxJQUFJOzs7Ozs7c0VBQ3JDLDhEQUFDM0I7OzhFQUNDLDhEQUFDQTtvRUFBSUMsV0FBVTs4RUFBNkJ5QixLQUFLRSxJQUFJOzs7Ozs7OEVBQ3JELDhEQUFDNUI7b0VBQUlDLFdBQVU7O3dFQUF5QnlCLEtBQUtqQyxhQUFhO3dFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUcvRCw4REFBQ087b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ0Q7Z0VBQ0NDLFdBQVcsQ0FBQyw2Q0FBNkMsRUFDdkR5QixLQUFLOUIsVUFBVSxJQUFJLEtBQUssaUJBQ3hCOEIsS0FBSzlCLFVBQVUsSUFBSSxLQUFLLGtCQUN4QjhCLEtBQUs5QixVQUFVLElBQUksS0FBSyxrQkFDeEI4QixLQUFLOUIsVUFBVSxHQUFHLElBQUksZUFBZSxlQUNyQztnRUFDRjRCLE9BQU87b0VBQUVLLE9BQU8sR0FBR0gsS0FBSzlCLFVBQVUsQ0FBQyxDQUFDLENBQUM7Z0VBQUM7Ozs7Ozs7Ozs7O3NFQUcxQyw4REFBQ3VCOzREQUFLbEIsV0FBVTs7Z0VBQXFEeUIsS0FBSzlCLFVBQVU7Z0VBQUM7Ozs7Ozs7Ozs7Ozs7OzJDQXBCL0U4QixLQUFLL0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkE2QnpCLDhEQUFDSztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNNOzRCQUNDQyxTQUFTNUQ7NEJBQ1RxRCxXQUFVO3NDQUNYOzs7Ozs7c0NBR0QsOERBQUNDOzRCQUFFRCxXQUFVOztnQ0FBb0M7OENBQzlCLDhEQUFDa0I7b0NBQUtsQixXQUFVOzhDQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNN0UiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXHNyY1xcY29tcG9uZW50c1xcV2Vla2x5UmVwb3J0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBERUZBVUxUX1JPVVRJTkVfSVRFTVMsIERhaWx5UHJvZ3Jlc3MsIGNhbGN1bGF0ZUNvbXBsZXRpb25SYXRlIH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyBnZXRVc2VyUHJvZ3Jlc3MgfSBmcm9tICdAL3V0aWxzL3N0b3JhZ2UnO1xuXG5pbnRlcmZhY2UgV2Vla2x5UmVwb3J0UHJvcHMge1xuICB1c2VySWQ6IHN0cmluZztcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbn1cblxuaW50ZXJmYWNlIFdlZWtseURhdGEge1xuICBkYXRlOiBzdHJpbmc7XG4gIGRheU5hbWU6IHN0cmluZztcbiAgcHJvZ3Jlc3M6IERhaWx5UHJvZ3Jlc3MgfCBudWxsO1xuICBjb21wbGV0aW9uUmF0ZTogbnVtYmVyO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gV2Vla2x5UmVwb3J0KHsgdXNlcklkLCBvbkNsb3NlIH06IFdlZWtseVJlcG9ydFByb3BzKSB7XG4gIGNvbnN0IFt3ZWVrbHlEYXRhLCBzZXRXZWVrbHlEYXRhXSA9IHVzZVN0YXRlPFdlZWtseURhdGFbXT4oW10pO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzZWxlY3RlZFdlZWssIHNldFNlbGVjdGVkV2Vla10gPSB1c2VTdGF0ZSgwKTsgLy8gMCA9IGN1cnJlbnQgd2VlaywgMSA9IGxhc3Qgd2VlaywgZXRjLlxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFdlZWtseURhdGEoKTtcbiAgfSwgW3VzZXJJZCwgc2VsZWN0ZWRXZWVrXSk7XG5cbiAgY29uc3QgbG9hZFdlZWtseURhdGEgPSAoKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIGNvbnN0IGRhdGE6IFdlZWtseURhdGFbXSA9IFtdO1xuICAgIFxuICAgIC8vIEdldCB0aGUgc3RhcnQgb2YgdGhlIHNlbGVjdGVkIHdlZWsgKFN1bmRheSlcbiAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgY3VycmVudERheSA9IHRvZGF5LmdldERheSgpOyAvLyAwID0gU3VuZGF5LCAxID0gTW9uZGF5LCBldGMuXG4gICAgY29uc3Qgc3RhcnRPZldlZWsgPSBuZXcgRGF0ZSh0b2RheSk7XG4gICAgc3RhcnRPZldlZWsuc2V0RGF0ZSh0b2RheS5nZXREYXRlKCkgLSBjdXJyZW50RGF5IC0gKHNlbGVjdGVkV2VlayAqIDcpKTtcbiAgICBcbiAgICAvLyBHZW5lcmF0ZSA3IGRheXMgb2YgZGF0YVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNzsgaSsrKSB7XG4gICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoc3RhcnRPZldlZWspO1xuICAgICAgZGF0ZS5zZXREYXRlKHN0YXJ0T2ZXZWVrLmdldERhdGUoKSArIGkpO1xuICAgICAgY29uc3QgZGF0ZVN0ciA9IGRhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgXG4gICAgICBjb25zdCBwcm9ncmVzcyA9IGdldFVzZXJQcm9ncmVzcyh1c2VySWQsIGRhdGVTdHIpO1xuICAgICAgY29uc3QgY29tcGxldGlvblJhdGUgPSBwcm9ncmVzcyBcbiAgICAgICAgPyBjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZShwcm9ncmVzcy5jb21wbGV0ZWRJdGVtcywgREVGQVVMVF9ST1VUSU5FX0lURU1TLmxlbmd0aClcbiAgICAgICAgOiAwO1xuICAgICAgXG4gICAgICBkYXRhLnB1c2goe1xuICAgICAgICBkYXRlOiBkYXRlU3RyLFxuICAgICAgICBkYXlOYW1lOiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7IHdlZWtkYXk6ICdzaG9ydCcgfSksXG4gICAgICAgIHByb2dyZXNzLFxuICAgICAgICBjb21wbGV0aW9uUmF0ZVxuICAgICAgfSk7XG4gICAgfVxuICAgIFxuICAgIHNldFdlZWtseURhdGEoZGF0YSk7XG4gICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBnZXRXZWVrVGl0bGUgPSAoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkV2VlayA9PT0gMCkgcmV0dXJuICdUaGlzIFdlZWsnO1xuICAgIGlmIChzZWxlY3RlZFdlZWsgPT09IDEpIHJldHVybiAnTGFzdCBXZWVrJztcbiAgICByZXR1cm4gYCR7c2VsZWN0ZWRXZWVrICsgMX0gV2Vla3MgQWdvYDtcbiAgfTtcblxuICBjb25zdCBnZXRXZWVrbHlBdmVyYWdlID0gKCkgPT4ge1xuICAgIGNvbnN0IHRvdGFsUmF0ZSA9IHdlZWtseURhdGEucmVkdWNlKChzdW0sIGRheSkgPT4gc3VtICsgZGF5LmNvbXBsZXRpb25SYXRlLCAwKTtcbiAgICByZXR1cm4gTWF0aC5yb3VuZCh0b3RhbFJhdGUgLyB3ZWVrbHlEYXRhLmxlbmd0aCk7XG4gIH07XG5cbiAgY29uc3QgZ2V0QmVzdERheSA9ICgpID0+IHtcbiAgICByZXR1cm4gd2Vla2x5RGF0YS5yZWR1Y2UoKGJlc3QsIGRheSkgPT4gXG4gICAgICBkYXkuY29tcGxldGlvblJhdGUgPiBiZXN0LmNvbXBsZXRpb25SYXRlID8gZGF5IDogYmVzdFxuICAgICk7XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RyZWFrRGF5cyA9ICgpID0+IHtcbiAgICByZXR1cm4gd2Vla2x5RGF0YS5maWx0ZXIoZGF5ID0+IGRheS5jb21wbGV0aW9uUmF0ZSA+IDApLmxlbmd0aDtcbiAgfTtcblxuICBjb25zdCBnZXRUYXNrU3RhdHMgPSAoKSA9PiB7XG4gICAgY29uc3QgdGFza1N0YXRzID0gREVGQVVMVF9ST1VUSU5FX0lURU1TLm1hcChpdGVtID0+IHtcbiAgICAgIGNvbnN0IGNvbXBsZXRlZERheXMgPSB3ZWVrbHlEYXRhLmZpbHRlcihkYXkgPT4gXG4gICAgICAgIGRheS5wcm9ncmVzcz8uY29tcGxldGVkSXRlbXMuaW5jbHVkZXMoaXRlbS5pZClcbiAgICAgICkubGVuZ3RoO1xuICAgICAgXG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5pdGVtLFxuICAgICAgICBjb21wbGV0ZWREYXlzLFxuICAgICAgICBwZXJjZW50YWdlOiBNYXRoLnJvdW5kKChjb21wbGV0ZWREYXlzIC8gNykgKiAxMDApXG4gICAgICB9O1xuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiB0YXNrU3RhdHMuc29ydCgoYSwgYikgPT4gYi5wZXJjZW50YWdlIC0gYS5wZXJjZW50YWdlKTtcbiAgfTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWJsYWNrIGJnLW9wYWNpdHktNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgd2Vla2x5IHJlcG9ydC4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIHAtNFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy14bCBtYXgtdy00eGwgdy1mdWxsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgIHNyYz1cIi9pY29uLnBuZ1wiXG4gICAgICAgICAgICAgIGFsdD1cIlJvdXRpbmUgVHJhY2tlclwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTAgaC0xMCByb3VuZGVkLWxnIHNoYWRvdy1tZFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+8J+TiiBXZWVrbHkgUmVwb3J0PC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPntnZXRXZWVrVGl0bGUoKX08L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgey8qIFdlZWsgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZFdlZWsoc2VsZWN0ZWRXZWVrICsgMSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJQcmV2aW91cyB3ZWVrXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPVwiMlwiIGQ9XCJNMTUgMTlsLTctNyA3LTdcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1pbi13LVsxMDBweF0gdGV4dC1jZW50ZXJcIj57Z2V0V2Vla1RpdGxlKCl9PC9zcGFuPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRXZWVrKE1hdGgubWF4KDAsIHNlbGVjdGVkV2VlayAtIDEpKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17c2VsZWN0ZWRXZWVrID09PSAwfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgIHRpdGxlPVwiTmV4dCB3ZWVrXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPVwiMlwiIGQ9XCJNOSA1bDcgNy03IDdcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPVwiMlwiIGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgey8qIFdlZWtseSBPdmVydmlldyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTQgbWItOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1pbmRpZ28tNTAgcm91bmRlZC1sZyBwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1pbmRpZ28tNjAwXCI+e2dldFdlZWtseUF2ZXJhZ2UoKX0lPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWluZGlnby03MDBcIj5XZWVrbHkgQXZlcmFnZTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIHJvdW5kZWQtbGcgcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+e2dldFN0cmVha0RheXMoKX0vNzwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi03MDBcIj5BY3RpdmUgRGF5czwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXB1cnBsZS01MCByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj57Z2V0QmVzdERheSgpLmNvbXBsZXRpb25SYXRlfSU8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHVycGxlLTcwMFwiPkJlc3QgRGF5ICh7Z2V0QmVzdERheSgpLmRheU5hbWV9KTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIERhaWx5IFByb2dyZXNzIENoYXJ0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5EYWlseSBQcm9ncmVzczwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgZ2FwLTJcIj5cbiAgICAgICAgICAgICAge3dlZWtseURhdGEubWFwKChkYXksIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgbWItMlwiPntkYXkuZGF5TmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgcm91bmRlZC1sZyBoLTI0IGZsZXggaXRlbXMtZW5kIGp1c3RpZnktY2VudGVyIHAtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCByb3VuZGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgZGF5LmNvbXBsZXRpb25SYXRlID49IDgwID8gJ2JnLWdyZWVuLTUwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgZGF5LmNvbXBsZXRpb25SYXRlID49IDYwID8gJ2JnLXllbGxvdy01MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGRheS5jb21wbGV0aW9uUmF0ZSA+PSA0MCA/ICdiZy1vcmFuZ2UtNTAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICBkYXkuY29tcGxldGlvblJhdGUgPiAwID8gJ2JnLXJlZC01MDAnIDogJ2JnLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogYCR7TWF0aC5tYXgoZGF5LmNvbXBsZXRpb25SYXRlLCA1KX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIG10LTFcIj57ZGF5LmNvbXBsZXRpb25SYXRlfSU8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICB7LyogVGFzayBQZXJmb3JtYW5jZSAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5UYXNrIFBlcmZvcm1hbmNlPC9oMz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIHtnZXRUYXNrU3RhdHMoKS5tYXAoKHRhc2spID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17dGFzay5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+e3Rhc2suaWNvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3Rhc2submFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPnt0YXNrLmNvbXBsZXRlZERheXN9LzcgZGF5cyBjb21wbGV0ZWQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2sucGVyY2VudGFnZSA+PSA4MCA/ICdiZy1ncmVlbi01MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzay5wZXJjZW50YWdlID49IDYwID8gJ2JnLXllbGxvdy01MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzay5wZXJjZW50YWdlID49IDQwID8gJ2JnLW9yYW5nZS01MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGFzay5wZXJjZW50YWdlID4gMCA/ICdiZy1yZWQtNTAwJyA6ICdiZy1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke3Rhc2sucGVyY2VudGFnZX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCB3LTEyIHRleHQtcmlnaHRcIj57dGFzay5wZXJjZW50YWdlfSU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogRm9vdGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBwLTYgYmctZ3JheS01MFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctaW5kaWdvLTYwMCB0ZXh0LXdoaXRlIHB5LTIgcHgtNCByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIGhvdmVyOmJnLWluZGlnby03MDAgdHJhbnNpdGlvbi1jb2xvcnMgbWItM1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ2xvc2UgUmVwb3J0XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICBCdWlsdCB3aXRoIOKdpO+4jyBieSA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtaW5kaWdvLTYwMFwiPlRlY2ggVGFsazwvc3Bhbj5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJERUZBVUxUX1JPVVRJTkVfSVRFTVMiLCJjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZSIsImdldFVzZXJQcm9ncmVzcyIsIldlZWtseVJlcG9ydCIsInVzZXJJZCIsIm9uQ2xvc2UiLCJ3ZWVrbHlEYXRhIiwic2V0V2Vla2x5RGF0YSIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInNlbGVjdGVkV2VlayIsInNldFNlbGVjdGVkV2VlayIsImxvYWRXZWVrbHlEYXRhIiwiZGF0YSIsInRvZGF5IiwiRGF0ZSIsImN1cnJlbnREYXkiLCJnZXREYXkiLCJzdGFydE9mV2VlayIsInNldERhdGUiLCJnZXREYXRlIiwiaSIsImRhdGUiLCJkYXRlU3RyIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInByb2dyZXNzIiwiY29tcGxldGlvblJhdGUiLCJjb21wbGV0ZWRJdGVtcyIsImxlbmd0aCIsInB1c2giLCJkYXlOYW1lIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwid2Vla2RheSIsImdldFdlZWtUaXRsZSIsImdldFdlZWtseUF2ZXJhZ2UiLCJ0b3RhbFJhdGUiLCJyZWR1Y2UiLCJzdW0iLCJkYXkiLCJNYXRoIiwicm91bmQiLCJnZXRCZXN0RGF5IiwiYmVzdCIsImdldFN0cmVha0RheXMiLCJmaWx0ZXIiLCJnZXRUYXNrU3RhdHMiLCJ0YXNrU3RhdHMiLCJtYXAiLCJpdGVtIiwiY29tcGxldGVkRGF5cyIsImluY2x1ZGVzIiwiaWQiLCJwZXJjZW50YWdlIiwic29ydCIsImEiLCJiIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImltZyIsInNyYyIsImFsdCIsImgyIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwic3BhbiIsIm1heCIsImRpc2FibGVkIiwiaDMiLCJpbmRleCIsInN0eWxlIiwiaGVpZ2h0IiwidGFzayIsImljb24iLCJuYW1lIiwid2lkdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WeeklyReport.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/WelcomeScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/WelcomeScreen.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WelcomeScreen: () => (/* binding */ WelcomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(ssr)/./src/utils/storage.ts\");\n/* harmony import */ var _config_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/auth */ \"(ssr)/./src/config/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ WelcomeScreen auto */ \n\n\n\n\nfunction WelcomeScreen({ onUserLogin }) {\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!name.trim()) return;\n        setIsLoading(true);\n        setError('');\n        try {\n            // Check if user is authorized\n            if (!(0,_config_auth__WEBPACK_IMPORTED_MODULE_4__.isUserAuthorized)(name.trim())) {\n                setError('Access denied. You are not authorized to use this tracker.');\n                setIsLoading(false);\n                return;\n            }\n            // Get the display name for authorized user\n            const displayName = (0,_config_auth__WEBPACK_IMPORTED_MODULE_4__.getUserDisplayName)(name.trim());\n            // Check if user already exists (try both input name and display name)\n            let user = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(name.trim()) || (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(displayName);\n            if (!user) {\n                // Create new user with consistent ID based on input name\n                user = {\n                    id: (0,_types__WEBPACK_IMPORTED_MODULE_2__.generateUserId)(name.trim()),\n                    name: displayName,\n                    createdAt: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)(),\n                    lastActive: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            } else {\n                // Update last active time and display name\n                user.lastActive = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)();\n                user.name = displayName; // Update to current display name\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            }\n            // Set as current user\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(user.id);\n            onUserLogin(user.id);\n        } catch (error) {\n            console.error('Error logging in:', error);\n            setError('An error occurred. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/icon.png\",\n                            alt: \"Routine Tracker\",\n                            className: \"w-20 h-20 mx-auto mb-4 rounded-lg shadow-md\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Digital Routine & Results Tracker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Track your daily growth with intention\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-indigo-600 font-semibold mt-2\",\n                            children: \"Built with ❤️ by Tech Talk\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"What were building here \\uD83D\\uDCBB✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Type in your name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tick what youve done for the day (Prayer, Study, Hygiene, Work, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Submit, and it saves your progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Come back anytime before the day ends to update it\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"System resets at midnight, but keeps a history of your growth\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                            children: \"Ready to track your growth? \\uD83D\\uDCCA✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"name\",\n                                            value: name,\n                                            onChange: (e)=>setName(e.target.value),\n                                            placeholder: \"Enter your name...\",\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors\",\n                                            disabled: isLoading,\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !name.trim(),\n                                    className: \"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Getting started...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this) : 'Start Tracking 🔥'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No login stress. No judgment. Just you, your goals, and your growth.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/WelcomeScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/auth.ts":
/*!****************************!*\
  !*** ./src/config/auth.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZED_USERS: () => (/* binding */ AUTHORIZED_USERS),\n/* harmony export */   getAllAuthorizedNames: () => (/* binding */ getAllAuthorizedNames),\n/* harmony export */   getAuthorizedUser: () => (/* binding */ getAuthorizedUser),\n/* harmony export */   getAuthorizedUsersCount: () => (/* binding */ getAuthorizedUsersCount),\n/* harmony export */   getUserDisplayName: () => (/* binding */ getUserDisplayName),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isUserAuthorized: () => (/* binding */ isUserAuthorized)\n/* harmony export */ });\n// Authorized users configuration for the Digital Routine & Results Tracker\n// Only users with names in this list can access the application\n// List of authorized users - Add names here to grant access\nconst AUTHORIZED_USERS = [\n    {\n        name: \"Obinna\",\n        displayName: \"Mchost\",\n        role: \"admin\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"Daniel\",\n        displayName: \"Aj danny Roze\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    }\n];\n// Utility functions for user authorization\nconst isUserAuthorized = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    return AUTHORIZED_USERS.some((user)=>user.name.toLowerCase() === normalizedInput);\n};\nconst getAuthorizedUser = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    return AUTHORIZED_USERS.find((user)=>user.name.toLowerCase() === normalizedInput) || null;\n};\nconst getUserDisplayName = (inputName)=>{\n    const user = getAuthorizedUser(inputName);\n    return user?.displayName || inputName;\n};\nconst isAdmin = (inputName)=>{\n    const user = getAuthorizedUser(inputName);\n    return user?.role === 'admin';\n};\n// Get total number of authorized users\nconst getAuthorizedUsersCount = ()=>{\n    return AUTHORIZED_USERS.length;\n};\n// Get all authorized user names (for admin purposes)\nconst getAllAuthorizedNames = ()=>{\n    return AUTHORIZED_USERS.map((user)=>user.displayName || user.name);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   getNigerianTimeDisplay: () => (/* binding */ getNigerianTimeDisplay),\n/* harmony export */   getRoutineItemsForDay: () => (/* binding */ getRoutineItemsForDay),\n/* harmony export */   getTodayRoutineItems: () => (/* binding */ getTodayRoutineItems),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection',\n        detailedInfo: {\n            purpose: 'Connect with the divine, find inner peace, and seek guidance for daily life.',\n            benefits: [\n                'Reduces stress and anxiety',\n                'Provides spiritual guidance and clarity',\n                'Strengthens faith and spiritual connection',\n                'Promotes gratitude and mindfulness',\n                'Offers comfort during difficult times'\n            ],\n            tips: [\n                'Set aside a quiet, dedicated space for prayer',\n                'Choose a consistent time each day',\n                'Start with gratitude and thanksgiving',\n                'Include prayers for others, not just yourself',\n                'Listen for guidance and inner peace'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development',\n        detailedInfo: {\n            purpose: 'Expand knowledge, develop skills, and pursue personal or professional growth through dedicated learning.',\n            benefits: [\n                'Improves cognitive function and memory',\n                'Enhances career prospects and opportunities',\n                'Builds confidence and self-esteem',\n                'Keeps mind sharp and engaged',\n                'Opens new perspectives and ideas'\n            ],\n            tips: [\n                'Create a distraction-free study environment',\n                'Use active learning techniques (notes, summaries)',\n                'Take regular breaks to maintain focus',\n                'Set specific, achievable learning goals',\n                'Review and practice regularly for retention'\n            ],\n            timeRecommendation: '30-120 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness',\n        detailedInfo: {\n            purpose: 'Maintain personal cleanliness, health, and confidence through proper hygiene practices.',\n            benefits: [\n                'Prevents illness and infections',\n                'Boosts self-confidence and social acceptance',\n                'Improves overall health and well-being',\n                'Creates positive first impressions',\n                'Reduces stress and anxiety about appearance'\n            ],\n            tips: [\n                'Establish a consistent daily routine',\n                'Use quality hygiene products suited for your skin type',\n                'Pay attention to often-missed areas (behind ears, between toes)',\n                'Replace hygiene items regularly (toothbrush, razors)',\n                'Stay hydrated to support healthy skin and hair'\n            ],\n            timeRecommendation: '20-45 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities',\n        detailedInfo: {\n            purpose: 'Accomplish professional goals, contribute value, and advance career through focused work.',\n            benefits: [\n                'Provides financial stability and security',\n                'Builds professional skills and experience',\n                'Creates sense of purpose and achievement',\n                'Develops problem-solving abilities',\n                'Expands professional network and opportunities'\n            ],\n            tips: [\n                'Set clear daily and weekly goals',\n                'Prioritize tasks using time management techniques',\n                'Take regular breaks to maintain productivity',\n                'Minimize distractions during focused work time',\n                'Continuously learn and improve your skills'\n            ],\n            timeRecommendation: '6-8 hours',\n            frequency: 'Weekdays'\n        }\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness',\n        detailedInfo: {\n            purpose: 'Maintain physical health, build strength, and improve overall well-being through regular exercise.',\n            benefits: [\n                'Improves cardiovascular health and endurance',\n                'Builds muscle strength and bone density',\n                'Enhances mental health and reduces stress',\n                'Boosts energy levels and sleep quality',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Start with activities you enjoy to build consistency',\n                'Gradually increase intensity and duration',\n                'Include both cardio and strength training',\n                'Stay hydrated before, during, and after exercise',\n                'Listen to your body and allow for rest days'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily or 5-6 times per week'\n        }\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning',\n        detailedInfo: {\n            purpose: 'Fuel your body with nutritious foods to support optimal health, energy, and well-being.',\n            benefits: [\n                'Provides essential nutrients for body functions',\n                'Maintains stable energy levels throughout the day',\n                'Supports immune system and disease prevention',\n                'Improves mental clarity and cognitive function',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Plan meals in advance to avoid unhealthy choices',\n                'Include a variety of colorful fruits and vegetables',\n                'Stay hydrated with plenty of water',\n                'Practice portion control and mindful eating',\n                'Limit processed foods and added sugars'\n            ],\n            timeRecommendation: '30-60 minutes for meal prep',\n            frequency: 'Daily meal planning and preparation'\n        }\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection',\n        detailedInfo: {\n            purpose: 'Gain self-awareness, process experiences, and promote personal growth through thoughtful reflection.',\n            benefits: [\n                'Increases self-awareness and emotional intelligence',\n                'Helps process and learn from experiences',\n                'Reduces stress and promotes mental clarity',\n                'Identifies patterns and areas for improvement',\n                'Enhances gratitude and positive mindset'\n            ],\n            tips: [\n                'Set aside quiet time without distractions',\n                'Write freely without worrying about grammar',\n                'Ask yourself meaningful questions about your day',\n                'Focus on both challenges and achievements',\n                'Review past entries to track growth and patterns'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily, preferably evening'\n        }\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions',\n        detailedInfo: {\n            purpose: 'Build and maintain meaningful relationships that provide support, joy, and personal growth.',\n            benefits: [\n                'Reduces feelings of loneliness and isolation',\n                'Provides emotional support and encouragement',\n                'Enhances mental health and well-being',\n                'Creates opportunities for learning and growth',\n                'Builds a strong support network for life challenges'\n            ],\n            tips: [\n                'Be present and actively listen during conversations',\n                'Reach out to friends and family regularly',\n                'Engage in shared activities and interests',\n                'Show genuine interest in others\\' lives and experiences',\n                'Practice empathy and offer support when needed'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily interactions, deeper connections weekly'\n        }\n    },\n    {\n        id: 'fasting',\n        name: 'Fasting',\n        icon: '🌙',\n        description: 'Spiritual fasting practice',\n        daysOfWeek: [\n            3,\n            5\n        ],\n        detailedInfo: {\n            purpose: 'Engage in spiritual discipline, self-control, and deeper connection with faith through fasting.',\n            benefits: [\n                'Develops self-discipline and willpower',\n                'Enhances spiritual awareness and focus',\n                'Promotes gratitude for daily blessings',\n                'Encourages prayer and meditation',\n                'Builds empathy for those less fortunate'\n            ],\n            tips: [\n                'Start with shorter fasting periods if new to fasting',\n                'Stay hydrated with water throughout the day',\n                'Use fasting time for prayer and reflection',\n                'Break your fast gently with light, healthy foods',\n                'Consult healthcare provider if you have medical conditions'\n            ],\n            timeRecommendation: 'Sunrise to sunset',\n            frequency: 'Wednesdays and Fridays'\n        }\n    }\n];\n// Get Nigerian time (West Africa Time - UTC+1)\nconst getNigerianTime = ()=>{\n    // Create a date in Nigerian timezone\n    const now = new Date();\n    const nigerianTimeString = now.toLocaleString('en-US', {\n        timeZone: 'Africa/Lagos'\n    });\n    return new Date(nigerianTimeString);\n};\n// Get routine items for a specific day (filters by day of week)\nconst getRoutineItemsForDay = (date)=>{\n    const targetDate = date || getNigerianTime();\n    const dayOfWeek = targetDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday\n    return DEFAULT_ROUTINE_ITEMS.filter((item)=>{\n        // If no specific days are defined, show the item every day\n        if (!item.daysOfWeek || item.daysOfWeek.length === 0) {\n            return true;\n        }\n        // Otherwise, only show on specified days\n        return item.daysOfWeek.includes(dayOfWeek);\n    });\n};\n// Get routine items for today\nconst getTodayRoutineItems = ()=>{\n    return getRoutineItemsForDay();\n};\n// Utility functions for date handling (using Nigerian time)\nconst getCurrentDate = ()=>{\n    return getNigerianTime().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return getNigerianTime().toISOString();\n};\n// Get current Nigerian time for display\nconst getNigerianTimeDisplay = ()=>{\n    return new Date().toLocaleDateString('en-US', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        timeZone: 'Africa/Lagos'\n    });\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (consistent based on name only)\nconst generateUserId = (name)=>{\n    // Create a consistent ID based on the name only (no timestamp)\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    // Add a simple hash to make it more unique but still consistent\n    let hash = 0;\n    for(let i = 0; i < name.length; i++){\n        const char = name.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return `${nameHash}-${Math.abs(hash)}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/storage.ts":
/*!******************************!*\
  !*** ./src/utils/storage.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStreak: () => (/* binding */ calculateStreak),\n/* harmony export */   checkAndPerformDailyReset: () => (/* binding */ checkAndPerformDailyReset),\n/* harmony export */   clearAllData: () => (/* binding */ clearAllData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getDailyProgress: () => (/* binding */ getDailyProgress),\n/* harmony export */   getHistoricalData: () => (/* binding */ getHistoricalData),\n/* harmony export */   getTodayProgress: () => (/* binding */ getTodayProgress),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserHistoricalData: () => (/* binding */ getUserHistoricalData),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   hashPin: () => (/* binding */ hashPin),\n/* harmony export */   saveDailyProgress: () => (/* binding */ saveDailyProgress),\n/* harmony export */   saveHistoricalData: () => (/* binding */ saveHistoricalData),\n/* harmony export */   saveUser: () => (/* binding */ saveUser),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setPinForUser: () => (/* binding */ setPinForUser),\n/* harmony export */   userHasPin: () => (/* binding */ userHasPin),\n/* harmony export */   verifyPin: () => (/* binding */ verifyPin)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n// Local storage utilities for the Digital Routine & Results Tracker\n\nconst STORAGE_KEYS = {\n    USERS: 'routine_tracker_users',\n    DAILY_PROGRESS: 'routine_tracker_daily_progress',\n    HISTORICAL_DATA: 'routine_tracker_historical_data',\n    CURRENT_USER: 'routine_tracker_current_user'\n};\n// User management\nconst saveUser = (user)=>{\n    if (true) return;\n    const users = getUsers();\n    const existingIndex = users.findIndex((u)=>u.id === user.id);\n    if (existingIndex >= 0) {\n        users[existingIndex] = user;\n    } else {\n        users.push(user);\n    }\n    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n};\nconst getUsers = ()=>{\n    if (true) return [];\n    const stored = localStorage.getItem(STORAGE_KEYS.USERS);\n    return stored ? JSON.parse(stored) : [];\n};\nconst getUserByName = (name)=>{\n    const users = getUsers();\n    return users.find((u)=>u.name.toLowerCase() === name.toLowerCase()) || null;\n};\nconst setCurrentUser = (userId)=>{\n    if (true) return;\n    localStorage.setItem(STORAGE_KEYS.CURRENT_USER, userId);\n};\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    return localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n};\n// PIN management functions\nconst hashPin = (pin)=>{\n    // Simple hash function for PIN (in production, use a proper hashing library)\n    let hash = 0;\n    for(let i = 0; i < pin.length; i++){\n        const char = pin.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString();\n};\nconst setPinForUser = (userId, pin)=>{\n    const users = getUsers();\n    const userIndex = users.findIndex((u)=>u.id === userId);\n    if (userIndex !== -1) {\n        users[userIndex].pin = hashPin(pin);\n        users[userIndex].hasPinSetup = true;\n        saveUsers(users);\n    }\n};\nconst verifyPin = (userId, pin)=>{\n    const users = getUsers();\n    const user = users.find((u)=>u.id === userId);\n    if (!user || !user.pin) return false;\n    return user.pin === hashPin(pin);\n};\nconst userHasPin = (userId)=>{\n    const users = getUsers();\n    const user = users.find((u)=>u.id === userId);\n    return !!(user && user.hasPinSetup);\n};\n// Daily progress management\nconst saveDailyProgress = (progress)=>{\n    if (true) return;\n    const allProgress = getDailyProgress();\n    const key = `${progress.userId}_${progress.date}`;\n    allProgress[key] = progress;\n    localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n};\nconst getDailyProgress = ()=>{\n    if (true) return {};\n    const stored = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getTodayProgress = (userId)=>{\n    const allProgress = getDailyProgress();\n    const key = `${userId}_${(0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)()}`;\n    return allProgress[key] || null;\n};\nconst getUserProgress = (userId, date)=>{\n    const allProgress = getDailyProgress();\n    const key = `${userId}_${date}`;\n    return allProgress[key] || null;\n};\n// Historical data management\nconst saveHistoricalData = (data)=>{\n    if (true) return;\n    const allHistorical = getHistoricalData();\n    const key = `${data.userId}_${data.date}`;\n    allHistorical[key] = data;\n    localStorage.setItem(STORAGE_KEYS.HISTORICAL_DATA, JSON.stringify(allHistorical));\n};\nconst getHistoricalData = ()=>{\n    if (true) return {};\n    const stored = localStorage.getItem(STORAGE_KEYS.HISTORICAL_DATA);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getUserHistoricalData = (userId, days = 30)=>{\n    const allHistorical = getHistoricalData();\n    const userHistorical = [];\n    // Get last N days of data\n    for(let i = 0; i < days; i++){\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().split('T')[0];\n        const key = `${userId}_${dateStr}`;\n        if (allHistorical[key]) {\n            userHistorical.push(allHistorical[key]);\n        }\n    }\n    return userHistorical.sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());\n};\n// Daily reset logic\nconst checkAndPerformDailyReset = (userId)=>{\n    const todayProgress = getTodayProgress(userId);\n    // If no progress for today, check if we need to archive yesterday's data\n    if (!todayProgress) {\n        const yesterday = new Date();\n        yesterday.setDate(yesterday.getDate() - 1);\n        const yesterdayStr = yesterday.toISOString().split('T')[0];\n        const yesterdayProgress = getUserProgress(userId, yesterdayStr);\n        if (yesterdayProgress) {\n            // Archive yesterday's progress to historical data\n            const historicalData = {\n                userId: yesterdayProgress.userId,\n                date: yesterdayProgress.date,\n                completedItems: yesterdayProgress.completedItems,\n                completionRate: Math.round(yesterdayProgress.completedItems.length / 8 * 100),\n                streak: calculateStreak(userId, yesterdayStr)\n            };\n            saveHistoricalData(historicalData);\n        }\n        return true; // New day, reset needed\n    }\n    return false; // Same day, no reset needed\n};\n// Calculate current streak\nconst calculateStreak = (userId, endDate)=>{\n    let streak = 0;\n    const date = new Date(endDate);\n    while(true){\n        const dateStr = date.toISOString().split('T')[0];\n        const progress = getUserProgress(userId, dateStr);\n        if (progress && progress.completedItems.length > 0) {\n            streak++;\n            date.setDate(date.getDate() - 1);\n        } else {\n            break;\n        }\n    }\n    return streak;\n};\n// Clear all data (for testing/reset)\nconst clearAllData = ()=>{\n    if (true) return;\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        localStorage.removeItem(key);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/storage.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDesktop%5Carchive-2025-06-05T142720%2B0200%5CPractice%5CRr%201.0%5Croutine-tracker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();