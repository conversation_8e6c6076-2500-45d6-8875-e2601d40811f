"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _WeeklyReport__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WeeklyReport */ \"(app-pages-browser)/./src/components/WeeklyReport.tsx\");\n/* harmony import */ var _TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TaskInfoPopup */ \"(app-pages-browser)/./src/components/TaskInfoPopup.tsx\");\n/* harmony import */ var _PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PinSetupPopup */ \"(app-pages-browser)/./src/components/PinSetupPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RoutineTracker(param) {\n    let { userId, onLogout } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWeeklyReport, setShowWeeklyReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayRoutineItems, setTodayRoutineItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPinSetup, setShowPinSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChangePinPopup, setShowChangePinPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    // Update routine items when day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const updateRoutineItems = {\n                \"RoutineTracker.useEffect.updateRoutineItems\": ()=>{\n                    setTodayRoutineItems((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n                }\n            }[\"RoutineTracker.useEffect.updateRoutineItems\"];\n            // Update immediately\n            updateRoutineItems();\n            // Set up interval to check for day change every minute\n            const interval = setInterval({\n                \"RoutineTracker.useEffect.interval\": ()=>{\n                    updateRoutineItems();\n                }\n            }[\"RoutineTracker.useEffect.interval\"], 60000); // Check every minute\n            return ({\n                \"RoutineTracker.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], []);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n            // Check if user needs PIN setup (only show once)\n            if (!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.userHasPin)(userId)) {\n                // Show PIN setup popup after a short delay\n                setTimeout(()=>{\n                    setShowPinSetup(true);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    const toggleMenu = ()=>{\n        setShowMenu(!showMenu);\n    };\n    const openWeeklyReport = ()=>{\n        setShowWeeklyReport(true);\n        setShowMenu(false);\n    };\n    const closeWeeklyReport = ()=>{\n        setShowWeeklyReport(false);\n    };\n    const handleSetPin = (pin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, pin);\n        setShowPinSetup(false);\n    };\n    const handleSkipPin = ()=>{\n        setShowPinSetup(false);\n    };\n    const openChangePinPopup = ()=>{\n        setShowChangePinPopup(true);\n        setShowMenu(false);\n    };\n    const handleChangePin = (newPin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, newPin);\n        setShowChangePinPopup(false);\n    };\n    const handleCancelChangePin = ()=>{\n        setShowChangePinPopup(false);\n    };\n    const openTaskInfo = (task)=>{\n        setSelectedTask(task);\n    };\n    const closeTaskInfo = ()=>{\n        setSelectedTask(null);\n    };\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"RoutineTracker.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.relative')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"RoutineTracker.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"RoutineTracker.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        showMenu\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, todayRoutineItems.length);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getNigerianTimeDisplay)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icon.png\",\n                                            alt: \"Routine Tracker\",\n                                            className: \"w-12 h-12 rounded-lg shadow-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.name) || 'Friend',\n                                                        \"! \\uD83D\\uDC4B\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: currentDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n                                                title: \"Menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openWeeklyReport,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCCA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Weekly Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openChangePinPopup,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDD10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Change PIN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC64\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Switch User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Todays Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        todayRoutineItems.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(completionRate, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayRoutineItems.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'),\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium \".concat(isCompleted ? 'text-green-800' : 'text-gray-800'),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.detailedInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openTaskInfo(item);\n                                                    },\n                                                    className: \"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors\",\n                                                    title: \"More info\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-indigo-600 text-sm font-bold\",\n                                                        children: \"ⓘ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center \".concat(isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'),\n                                                    children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! Youve completed all your routines today!\" : completionRate >= 75 ? \"🔥 Youre doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! Youre halfway there!\" : \"🌱 Every step counts. Youve got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: \"Progress auto-saves • Resets at midnight • Access Weekly Report from menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                showWeeklyReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyReport__WEBPACK_IMPORTED_MODULE_4__.WeeklyReport, {\n                    userId: userId,\n                    onClose: closeWeeklyReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, this),\n                selectedTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__.TaskInfoPopup, {\n                    item: selectedTask,\n                    onClose: closeTaskInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 11\n                }, this),\n                showPinSetup && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__.PinSetupPopup, {\n                    userName: user.name,\n                    onSetPin: handleSetPin,\n                    onSkip: handleSkipPin\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutineTracker, \"kbzo4lGe7RTAtrBVA7iPH3EIf1k=\");\n_c = RoutineTracker;\nvar _c;\n$RefreshReg$(_c, \"RoutineTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RoutineTracker.tsx\n"));

/***/ })

});