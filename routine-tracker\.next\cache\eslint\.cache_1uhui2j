[{"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx": "3", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx": "4", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx": "5", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx": "6", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts": "7", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts": "9", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts": "10", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinInputScreen.tsx": "11", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinSetupPopup.tsx": "12"}, {"size": 689, "mtime": 1752249449047, "results": "13", "hashOfConfig": "14"}, {"size": 3121, "mtime": 1752250512136, "results": "15", "hashOfConfig": "14"}, {"size": 14309, "mtime": 1752250625176, "results": "16", "hashOfConfig": "14"}, {"size": 4791, "mtime": 1752158286771, "results": "17", "hashOfConfig": "14"}, {"size": 10123, "mtime": 1752166006347, "results": "18", "hashOfConfig": "14"}, {"size": 6205, "mtime": 1752250227082, "results": "19", "hashOfConfig": "14"}, {"size": 1918, "mtime": 1752157934473, "results": "20", "hashOfConfig": "14"}, {"size": 12024, "mtime": 1752250357146, "results": "21", "hashOfConfig": "14"}, {"size": 5172, "mtime": 1752187602092, "results": "22", "hashOfConfig": "14"}, {"size": 6546, "mtime": 1752251277242, "results": "23", "hashOfConfig": "14"}, {"size": 5321, "mtime": 1752251478628, "results": "24", "hashOfConfig": "14"}, {"size": 5259, "mtime": 1752273139946, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9cy87n", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx", ["62"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx", ["63", "64", "65"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx", ["66", "67"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx", ["68", "69"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx", ["70"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinInputScreen.tsx", ["71", "72"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinSetupPopup.tsx", [], [], {"ruleId": "73", "severity": 1, "message": "74", "line": 68, "column": 11, "nodeType": "75", "endLine": 72, "endColumn": 13}, {"ruleId": "76", "severity": 1, "message": "77", "line": 43, "column": 6, "nodeType": "78", "endLine": 43, "endColumn": 14, "suggestions": "79"}, {"ruleId": "73", "severity": 1, "message": "74", "line": 186, "column": 11, "nodeType": "75", "endLine": 190, "endColumn": 13}, {"ruleId": "73", "severity": 1, "message": "74", "line": 227, "column": 15, "nodeType": "75", "endLine": 231, "endColumn": 17}, {"ruleId": "76", "severity": 1, "message": "80", "line": 31, "column": 6, "nodeType": "78", "endLine": 31, "endColumn": 8, "suggestions": "81"}, {"ruleId": "76", "severity": 1, "message": "80", "line": 46, "column": 6, "nodeType": "78", "endLine": 46, "endColumn": 8, "suggestions": "82"}, {"ruleId": "76", "severity": 1, "message": "83", "line": 26, "column": 6, "nodeType": "78", "endLine": 26, "endColumn": 28, "suggestions": "84"}, {"ruleId": "73", "severity": 1, "message": "74", "line": 115, "column": 13, "nodeType": "75", "endLine": 119, "endColumn": 15}, {"ruleId": "73", "severity": 1, "message": "74", "line": 70, "column": 11, "nodeType": "75", "endLine": 74, "endColumn": 13}, {"ruleId": "76", "severity": 1, "message": "85", "line": 21, "column": 6, "nodeType": "78", "endLine": 21, "endColumn": 11, "suggestions": "86"}, {"ruleId": "73", "severity": 1, "message": "74", "line": 55, "column": 11, "nodeType": "75", "endLine": 59, "endColumn": 13}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeTracker'. Either include it or remove the dependency array.", "ArrayExpression", ["87"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["88"], ["89"], "React Hook useEffect has a missing dependency: 'loadWeeklyData'. Either include it or remove the dependency array.", ["90"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["91"], {"desc": "92", "fix": "93"}, {"desc": "94", "fix": "95"}, {"desc": "94", "fix": "96"}, {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, "Update the dependencies array to be: [initializeTracker, userId]", {"range": "101", "text": "102"}, "Update the dependencies array to be: [handleClose]", {"range": "103", "text": "104"}, {"range": "105", "text": "104"}, "Update the dependencies array to be: [userId, selectedWeek, loadWeeklyData]", {"range": "106", "text": "107"}, "Update the dependencies array to be: [handleSubmit, pin]", {"range": "108", "text": "109"}, [1334, 1342], "[initializeTracker, userId]", [816, 818], "[handleClose]", [1150, 1152], [729, 751], "[userId, selectedWeek, loadWeeklyData]", [506, 511], "[handleSubmit, pin]"]