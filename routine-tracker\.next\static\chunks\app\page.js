/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVVNFUiU1QyU1Q0Rlc2t0b3AlNUMlNUNhcmNoaXZlLTIwMjUtMDYtMDVUMTQyNzIwJTJCMDIwMCU1QyU1Q1ByYWN0aWNlJTVDJTVDUnIlMjAxLjAlNUMlNUNyb3V0aW5lLXRyYWNrZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNFUlxcXFxEZXNrdG9wXFxcXGFyY2hpdmUtMjAyNS0wNi0wNVQxNDI3MjArMDIwMFxcXFxQcmFjdGljZVxcXFxSciAxLjBcXFxccm91dGluZS10cmFja2VyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RoutineTracker */ \"(app-pages-browser)/./src/components/RoutineTracker.tsx\");\n/* harmony import */ var _components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/WelcomeScreen */ \"(app-pages-browser)/./src/components/WelcomeScreen.tsx\");\n/* harmony import */ var _components_PinInputScreen__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PinInputScreen */ \"(app-pages-browser)/./src/components/PinInputScreen.tsx\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPinInput, setShowPinInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingUserId, setPendingUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pinError, setPinError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Check if user is already logged in\n            const userId = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getCurrentUser)();\n            setCurrentUserId(userId);\n            setIsLoading(false);\n        }\n    }[\"Home.useEffect\"], []);\n    const handleUserLogin = (userId)=>{\n        // Check if user has PIN setup\n        if ((0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.userHasPin)(userId)) {\n            // Show PIN input screen\n            setPendingUserId(userId);\n            setShowPinInput(true);\n            setPinError('');\n        } else {\n            // Direct login (no PIN required)\n            setCurrentUserId(userId);\n        }\n    };\n    const handlePinSubmit = (pin)=>{\n        if (!pendingUserId) return;\n        if ((0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.verifyPin)(pendingUserId, pin)) {\n            // PIN correct, log in user\n            setCurrentUserId(pendingUserId);\n            setShowPinInput(false);\n            setPendingUserId(null);\n            setPinError('');\n        } else {\n            // PIN incorrect\n            setPinError('Incorrect PIN. Please try again.');\n        }\n    };\n    const handlePinBack = ()=>{\n        setShowPinInput(false);\n        setPendingUserId(null);\n        setPinError('');\n    };\n    const getPendingUserName = ()=>{\n        if (!pendingUserId) return '';\n        const users = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getUsers)();\n        const user = users.find((u)=>u.id === pendingUserId);\n        return (user === null || user === void 0 ? void 0 : user.name) || '';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your routine tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    // Show PIN input screen if needed\n    if (showPinInput && pendingUserId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PinInputScreen__WEBPACK_IMPORTED_MODULE_4__.PinInputScreen, {\n            userName: getPendingUserName(),\n            onPinSubmit: handlePinSubmit,\n            onBack: handlePinBack,\n            error: pinError\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: currentUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__.RoutineTracker, {\n            userId: currentUserId,\n            onLogout: ()=>setCurrentUserId(null)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__.WelcomeScreen, {\n            onUserLogin: handleUserLogin\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"9+AK3vPCwU2GpPzEex5n+4FjjyM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PinInputScreen.tsx":
/*!*******************************************!*\
  !*** ./src/components/PinInputScreen.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PinInputScreen: () => (/* binding */ PinInputScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PinInputScreen auto */ \nvar _s = $RefreshSig$();\n\nfunction PinInputScreen(param) {\n    let { userName, onPinSubmit, onBack, error } = param;\n    _s();\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Auto-submit when 4 digits are entered\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PinInputScreen.useEffect\": ()=>{\n            if (pin.length === 4) {\n                handleSubmit();\n            }\n        }\n    }[\"PinInputScreen.useEffect\"], [\n        pin\n    ]);\n    const handleSubmit = async ()=>{\n        if (pin.length !== 4) return;\n        setIsLoading(true);\n        try {\n            onPinSubmit(pin);\n        } catch (error) {\n            console.error('PIN submission error:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePinChange = (value)=>{\n        // Only allow numbers and limit to 4 digits\n        const numericValue = value.replace(/\\D/g, '').slice(0, 4);\n        setPin(numericValue);\n    };\n    const handleKeypadClick = (digit)=>{\n        if (pin.length < 4) {\n            setPin((prev)=>prev + digit);\n        }\n    };\n    const handleBackspace = ()=>{\n        setPin((prev)=>prev.slice(0, -1));\n    };\n    const handleClear = ()=>{\n        setPin('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/icon.png\",\n                            alt: \"Routine Tracker\",\n                            className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: [\n                                \"Welcome back, \",\n                                userName,\n                                \"! \\uD83D\\uDC4B\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Enter your 4-digit PIN to continue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4 mb-6\",\n                            children: [\n                                0,\n                                1,\n                                2,\n                                3\n                            ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 rounded-lg border-2 flex items-center justify-center text-2xl font-bold \".concat(pin.length > index ? 'border-indigo-500 bg-indigo-50 text-indigo-600' : 'border-gray-300 bg-gray-50'),\n                                    children: pin.length > index ? '•' : ''\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm text-center\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-3 mb-4\",\n                            children: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5,\n                                6,\n                                7,\n                                8,\n                                9\n                            ].map((digit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleKeypadClick(digit.toString()),\n                                    disabled: pin.length >= 4 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: digit\n                                }, digit, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClear,\n                                    disabled: pin.length === 0 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"Clear\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleKeypadClick('0'),\n                                    disabled: pin.length >= 4 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-semibold text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackspace,\n                                    disabled: pin.length === 0 || isLoading,\n                                    className: \"h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"⌫\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 text-indigo-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"Verifying...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        disabled: isLoading,\n                        className: \"text-indigo-600 hover:text-indigo-700 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"← Back to name entry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinInputScreen.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(PinInputScreen, \"vDmys7sPkDn0rTxonI65q2Gcz+o=\");\n_c = PinInputScreen;\nvar _c;\n$RefreshReg$(_c, \"PinInputScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PinInputScreen.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PinSetupPopup.tsx":
/*!******************************************!*\
  !*** ./src/components/PinSetupPopup.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PinSetupPopup: () => (/* binding */ PinSetupPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PinSetupPopup auto */ \nvar _s = $RefreshSig$();\n\nfunction PinSetupPopup(param) {\n    let { onSetPin, onSkip, userName } = param;\n    _s();\n    const [pin, setPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPin, setConfirmPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (pin.length !== 4) {\n            setError('PIN must be exactly 4 digits');\n            return;\n        }\n        if (!/^\\d{4}$/.test(pin)) {\n            setError('PIN must contain only numbers');\n            return;\n        }\n        if (pin !== confirmPin) {\n            setError('PINs do not match');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            onSetPin(pin);\n        } catch (error) {\n            setError('Failed to set PIN. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83D\\uDD10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Secure Your Account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Hi \",\n                                userName,\n                                \"! Set up a 4-digit PIN to protect your progress from unauthorized access.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-blue-900 mb-2\",\n                            children: \"Why set a PIN?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-sm text-blue-800 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Prevent others from accessing your account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Keep your progress private and secure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"• Quick and easy to enter (just 4 digits)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"pin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Enter 4-digit PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"pin\",\n                                    value: pin,\n                                    onChange: (e)=>setPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"confirmPin\",\n                                    value: confirmPin,\n                                    onChange: (e)=>setConfirmPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onSkip,\n                                    disabled: isLoading,\n                                    className: \"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"Skip for now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !pin || !confirmPin,\n                                    className: \"flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Setting up...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this) : 'Set PIN 🔒'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500 text-center mt-4\",\n                    children: \"You can change or remove your PIN anytime from the menu\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\PinSetupPopup.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(PinSetupPopup, \"IU+2Tq9AXbuAjjfQDM4dRhE6cVQ=\");\n_c = PinSetupPopup;\nvar _c;\n$RefreshReg$(_c, \"PinSetupPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PinSetupPopup.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _WeeklyReport__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WeeklyReport */ \"(app-pages-browser)/./src/components/WeeklyReport.tsx\");\n/* harmony import */ var _TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TaskInfoPopup */ \"(app-pages-browser)/./src/components/TaskInfoPopup.tsx\");\n/* harmony import */ var _PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PinSetupPopup */ \"(app-pages-browser)/./src/components/PinSetupPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RoutineTracker(param) {\n    let { userId, onLogout } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWeeklyReport, setShowWeeklyReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayRoutineItems, setTodayRoutineItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPinSetup, setShowPinSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    // Update routine items when day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const updateRoutineItems = {\n                \"RoutineTracker.useEffect.updateRoutineItems\": ()=>{\n                    setTodayRoutineItems((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n                }\n            }[\"RoutineTracker.useEffect.updateRoutineItems\"];\n            // Update immediately\n            updateRoutineItems();\n            // Set up interval to check for day change every minute\n            const interval = setInterval({\n                \"RoutineTracker.useEffect.interval\": ()=>{\n                    updateRoutineItems();\n                }\n            }[\"RoutineTracker.useEffect.interval\"], 60000); // Check every minute\n            return ({\n                \"RoutineTracker.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], []);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n            // Check if user needs PIN setup (only show once)\n            if (!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.userHasPin)(userId)) {\n                // Show PIN setup popup after a short delay\n                setTimeout(()=>{\n                    setShowPinSetup(true);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    const toggleMenu = ()=>{\n        setShowMenu(!showMenu);\n    };\n    const openWeeklyReport = ()=>{\n        setShowWeeklyReport(true);\n        setShowMenu(false);\n    };\n    const closeWeeklyReport = ()=>{\n        setShowWeeklyReport(false);\n    };\n    const handleSetPin = (pin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, pin);\n        setShowPinSetup(false);\n    };\n    const handleSkipPin = ()=>{\n        setShowPinSetup(false);\n    };\n    const openTaskInfo = (task)=>{\n        setSelectedTask(task);\n    };\n    const closeTaskInfo = ()=>{\n        setSelectedTask(null);\n    };\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"RoutineTracker.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.relative')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"RoutineTracker.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"RoutineTracker.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        showMenu\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, todayRoutineItems.length);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getNigerianTimeDisplay)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icon.png\",\n                                            alt: \"Routine Tracker\",\n                                            className: \"w-12 h-12 rounded-lg shadow-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.name) || 'Friend',\n                                                        \"! \\uD83D\\uDC4B\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: currentDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n                                                title: \"Menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openWeeklyReport,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCCA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Weekly Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC64\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Switch User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Todays Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        todayRoutineItems.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(completionRate, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayRoutineItems.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'),\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium \".concat(isCompleted ? 'text-green-800' : 'text-gray-800'),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.detailedInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openTaskInfo(item);\n                                                    },\n                                                    className: \"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors\",\n                                                    title: \"More info\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-indigo-600 text-sm font-bold\",\n                                                        children: \"ⓘ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center \".concat(isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'),\n                                                    children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! Youve completed all your routines today!\" : completionRate >= 75 ? \"🔥 Youre doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! Youre halfway there!\" : \"🌱 Every step counts. Youve got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: \"Progress auto-saves • Resets at midnight • Access Weekly Report from menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this),\n                showWeeklyReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyReport__WEBPACK_IMPORTED_MODULE_4__.WeeklyReport, {\n                    userId: userId,\n                    onClose: closeWeeklyReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this),\n                selectedTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__.TaskInfoPopup, {\n                    item: selectedTask,\n                    onClose: closeTaskInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this),\n                showPinSetup && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__.PinSetupPopup, {\n                    userName: user.name,\n                    onSetPin: handleSetPin,\n                    onSkip: handleSkipPin\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutineTracker, \"6zdwML8RTS8jYarve9NpMnnmxzQ=\");\n_c = RoutineTracker;\nvar _c;\n$RefreshReg$(_c, \"RoutineTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1JvdXRpbmVUcmFja2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUU0QztBQVMzQjtBQVFRO0FBQ3FCO0FBQ0U7QUFDQTtBQU96QyxTQUFTZ0IsZUFBZSxLQUF5QztRQUF6QyxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBdUIsR0FBekM7O0lBQzdCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHcEIsK0NBQVFBLENBQXVCO0lBQy9ELE1BQU0sQ0FBQ3FCLFdBQVdDLGFBQWEsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3VCLFVBQVVDLFlBQVksR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3lCLE1BQU1DLFFBQVEsR0FBRzFCLCtDQUFRQSxDQUFzQztJQUN0RSxNQUFNLENBQUMyQixVQUFVQyxZQUFZLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUM2QixrQkFBa0JDLG9CQUFvQixHQUFHOUIsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDK0IsbUJBQW1CQyxxQkFBcUIsR0FBR2hDLCtDQUFRQSxDQUFDSyw0REFBb0JBO0lBQy9FLE1BQU0sQ0FBQzRCLGNBQWNDLGdCQUFnQixHQUFHbEMsK0NBQVFBLENBQXFCO0lBQ3JFLE1BQU0sQ0FBQ21DLGNBQWNDLGdCQUFnQixHQUFHcEMsK0NBQVFBLENBQUM7SUFFakRDLGdEQUFTQTtvQ0FBQztZQUNSb0M7UUFDRjttQ0FBRztRQUFDcEI7S0FBTztJQUVYLHdDQUF3QztJQUN4Q2hCLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU1xQzsrREFBcUI7b0JBQ3pCTixxQkFBcUIzQiw0REFBb0JBO2dCQUMzQzs7WUFFQSxxQkFBcUI7WUFDckJpQztZQUVBLHVEQUF1RDtZQUN2RCxNQUFNQyxXQUFXQztxREFBWTtvQkFDM0JGO2dCQUNGO29EQUFHLFFBQVEscUJBQXFCO1lBRWhDOzRDQUFPLElBQU1HLGNBQWNGOztRQUM3QjttQ0FBRyxFQUFFO0lBRUwsTUFBTUYsb0JBQW9CO1FBQ3hCZixhQUFhO1FBRWIsSUFBSTtZQUNGLHdCQUF3QjtZQUN4QmIseUVBQXlCQSxDQUFDUTtZQUUxQixnQkFBZ0I7WUFDaEIsTUFBTXlCLFFBQVFDLEtBQUtDLEtBQUssQ0FBQ0MsYUFBYUMsT0FBTyxDQUFDLDRCQUE0QjtZQUMxRSxNQUFNQyxjQUFjTCxNQUFNTSxJQUFJLENBQUMsQ0FBQ0MsSUFBb0NBLEVBQUVDLEVBQUUsS0FBS2pDO1lBQzdFUyxRQUFRcUI7WUFFUix1QkFBdUI7WUFDdkIsTUFBTUksZ0JBQWdCNUMsZ0VBQWdCQSxDQUFDVTtZQUV2QyxJQUFJa0MsZUFBZTtnQkFDakIvQixZQUFZK0I7WUFDZCxPQUFPO2dCQUNMLGdDQUFnQztnQkFDaEMsTUFBTUMsY0FBNkI7b0JBQ2pDbkM7b0JBQ0FvQyxNQUFNbkQsc0RBQWNBO29CQUNwQm9ELGdCQUFnQixFQUFFO29CQUNsQkMsYUFBYXBELDJEQUFtQkE7Z0JBQ2xDO2dCQUNBaUIsWUFBWWdDO2dCQUNaNUMsaUVBQWlCQSxDQUFDNEM7WUFDcEI7WUFFQSxpREFBaUQ7WUFDakQsSUFBSSxDQUFDekMsMERBQVVBLENBQUNNLFNBQVM7Z0JBQ3ZCLDJDQUEyQztnQkFDM0N1QyxXQUFXO29CQUNUcEIsZ0JBQWdCO2dCQUNsQixHQUFHO1lBQ0w7UUFDRixFQUFFLE9BQU9xQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQy9DLFNBQVU7WUFDUm5DLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTXFDLGFBQWEsT0FBT0M7UUFDeEIsSUFBSSxDQUFDekMsWUFBWUksVUFBVTtRQUUzQkMsWUFBWTtRQUVaLElBQUk7WUFDRixNQUFNcUMsZUFBZTFDLFNBQVNtQyxjQUFjLENBQUNRLFFBQVEsQ0FBQ0YsVUFDbER6QyxTQUFTbUMsY0FBYyxDQUFDUyxNQUFNLENBQUNiLENBQUFBLEtBQU1BLE9BQU9VLFVBQzVDO21CQUFJekMsU0FBU21DLGNBQWM7Z0JBQUVNO2FBQU87WUFFeEMsTUFBTUksa0JBQWlDO2dCQUNyQyxHQUFHN0MsUUFBUTtnQkFDWG1DLGdCQUFnQk87Z0JBQ2hCTixhQUFhcEQsMkRBQW1CQTtZQUNsQztZQUVBaUIsWUFBWTRDO1lBQ1p4RCxpRUFBaUJBLENBQUN3RDtRQUNwQixFQUFFLE9BQU9QLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUMsU0FBVTtZQUNSakMsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNeUMsZUFBZTtRQUNuQnZELDhEQUFjQSxDQUFDO1FBQ2ZRO0lBQ0Y7SUFFQSxNQUFNZ0QsYUFBYTtRQUNqQnRDLFlBQVksQ0FBQ0Q7SUFDZjtJQUVBLE1BQU13QyxtQkFBbUI7UUFDdkJyQyxvQkFBb0I7UUFDcEJGLFlBQVk7SUFDZDtJQUVBLE1BQU13QyxvQkFBb0I7UUFDeEJ0QyxvQkFBb0I7SUFDdEI7SUFFQSxNQUFNdUMsZUFBZSxDQUFDQztRQUNwQjFELDZEQUFhQSxDQUFDSyxRQUFRcUQ7UUFDdEJsQyxnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNbUMsZ0JBQWdCO1FBQ3BCbkMsZ0JBQWdCO0lBQ2xCO0lBRUEsTUFBTW9DLGVBQWUsQ0FBQ0M7UUFDcEJ2QyxnQkFBZ0J1QztJQUNsQjtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQnhDLGdCQUFnQjtJQUNsQjtJQUVBLG1DQUFtQztJQUNuQ2pDLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU0wRTsrREFBcUIsQ0FBQ0M7b0JBQzFCLElBQUlqRCxVQUFVO3dCQUNaLE1BQU1rRCxTQUFTRCxNQUFNQyxNQUFNO3dCQUMzQixJQUFJLENBQUNBLE9BQU9DLE9BQU8sQ0FBQyxjQUFjOzRCQUNoQ2xELFlBQVk7d0JBQ2Q7b0JBQ0Y7Z0JBQ0Y7O1lBRUFtRCxTQUFTQyxnQkFBZ0IsQ0FBQyxhQUFhTDtZQUN2Qzs0Q0FBTztvQkFDTEksU0FBU0UsbUJBQW1CLENBQUMsYUFBYU47Z0JBQzVDOztRQUNGO21DQUFHO1FBQUNoRDtLQUFTO0lBRWIsSUFBSU4sV0FBVztRQUNiLHFCQUNFLDhEQUFDNkQ7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFDQ0MsS0FBSTt3QkFDSkMsS0FBSTt3QkFDSkgsV0FBVTs7Ozs7O2tDQUVaLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDSTt3QkFBRUosV0FBVTtrQ0FBcUI7Ozs7OztrQ0FDbEMsOERBQUNJO3dCQUFFSixXQUFVO2tDQUF3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNN0Q7SUFFQSxJQUFJLENBQUNoRSxVQUFVO1FBQ2IscUJBQ0UsOERBQUMrRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNJO3dCQUFFSixXQUFVO2tDQUFlOzs7Ozs7a0NBQzVCLDhEQUFDSzt3QkFDQ0MsU0FBU3BEO3dCQUNUOEMsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNVDtJQUVBLE1BQU1PLGlCQUFpQnRGLCtEQUF1QkEsQ0FBQ2UsU0FBU21DLGNBQWMsRUFBRXZCLGtCQUFrQjRELE1BQU07SUFDaEcsTUFBTUMsY0FBY3RGLDhEQUFzQkE7SUFFMUMscUJBQ0UsOERBQUM0RTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0M7NENBQ0NDLEtBQUk7NENBQ0pDLEtBQUk7NENBQ0pILFdBQVU7Ozs7OztzREFFWiw4REFBQ0Q7OzhEQUNDLDhEQUFDVztvREFBR1YsV0FBVTs7d0RBQW1DO3dEQUNoQzFELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXFFLElBQUksS0FBSTt3REFBUzs7Ozs7Ozs4REFFeEMsOERBQUNQO29EQUFFSixXQUFVOzhEQUFpQlM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHbEMsOERBQUNWO29DQUFJQyxXQUFVOzhDQUViLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNLO2dEQUNDQyxTQUFTdkI7Z0RBQ1RpQixXQUFVO2dEQUNWWSxPQUFNOzBEQUVOLDRFQUFDQztvREFBSWIsV0FBVTtvREFBVWMsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDakUsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBWTt3REFBSUMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FLeEU3RSwwQkFDQyw4REFBQ3VEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0s7d0RBQ0NDLFNBQVN0Qjt3REFDVGdCLFdBQVU7OzBFQUVWLDhEQUFDc0I7MEVBQUs7Ozs7OzswRUFDTiw4REFBQ0E7MEVBQUs7Ozs7Ozs7Ozs7OztrRUFFUiw4REFBQ3ZCO3dEQUFJQyxXQUFVOzs7Ozs7a0VBQ2YsOERBQUNLO3dEQUNDQyxTQUFTeEI7d0RBQ1RrQixXQUFVOzswRUFFViw4REFBQ3NCOzBFQUFLOzs7Ozs7MEVBQ04sOERBQUNBOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTbEIsOERBQUN2Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDSztvREFBRUosV0FBVTs4REFBd0I7Ozs7Ozs4REFDckMsOERBQUNJO29EQUFFSixXQUFVOzt3REFDVmhFLFNBQVNtQyxjQUFjLENBQUNxQyxNQUFNO3dEQUFDO3dEQUFFNUQsa0JBQWtCNEQsTUFBTTs7Ozs7Ozs7Ozs7OztzREFHOUQsOERBQUNUOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0k7b0RBQUVKLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3JDLDhEQUFDSTtvREFBRUosV0FBVTs7d0RBQXNDTzt3REFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLdEUsOERBQUNSO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQ0NDLFdBQVU7NENBQ1Z1QixPQUFPO2dEQUFFQyxPQUFPLEdBQWtCLE9BQWZqQixnQkFBZTs0Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFRL0MsOERBQUNSO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3lCOzRCQUFHekIsV0FBVTtzQ0FBMkM7Ozs7OztzQ0FJekQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNacEQsa0JBQWtCOEUsR0FBRyxDQUFDLENBQUNDO2dDQUN0QixNQUFNQyxjQUFjNUYsU0FBU21DLGNBQWMsQ0FBQ1EsUUFBUSxDQUFDZ0QsS0FBSzVELEVBQUU7Z0NBRTVELHFCQUNFLDhEQUFDZ0M7b0NBRUNDLFdBQVcsMkVBSVYsT0FIQzRCLGNBQ0ksaUNBQ0E7b0NBRU50QixTQUFTLElBQU05QixXQUFXbUQsS0FBSzVELEVBQUU7O3NEQUVqQyw4REFBQ2dDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQVkyQixLQUFLRSxJQUFJOzs7Ozs7OERBQ3BDLDhEQUFDOUI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDOEI7NERBQUc5QixXQUFXLGVBQWdFLE9BQWpENEIsY0FBYyxtQkFBbUI7c0VBQzVERCxLQUFLaEIsSUFBSTs7Ozs7O3NFQUVaLDhEQUFDUDs0REFBRUosV0FBVyxXQUE0RCxPQUFqRDRCLGNBQWMsbUJBQW1CO3NFQUN2REQsS0FBS0ksV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUt2Qiw4REFBQ2hDOzRDQUFJQyxXQUFVOztnREFFWjJCLEtBQUtLLFlBQVksa0JBQ2hCLDhEQUFDM0I7b0RBQ0NDLFNBQVMsQ0FBQzJCO3dEQUNSQSxFQUFFQyxlQUFlO3dEQUNqQjdDLGFBQWFzQztvREFDZjtvREFDQTNCLFdBQVU7b0RBQ1ZZLE9BQU07OERBRU4sNEVBQUNVO3dEQUFLdEIsV0FBVTtrRUFBb0M7Ozs7Ozs7Ozs7OzhEQUt4RCw4REFBQ0Q7b0RBQUlDLFdBQVcsa0VBSWYsT0FIQzRCLGNBQ0ksa0NBQ0E7OERBRUhBLDZCQUNDLDhEQUFDZjt3REFBSWIsV0FBVTt3REFBcUJjLE1BQUs7d0RBQWVFLFNBQVE7a0VBQzlELDRFQUFDQzs0REFBS2tCLFVBQVM7NERBQVVkLEdBQUU7NERBQXFIZSxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQ0EzQzVKVCxLQUFLNUQsRUFBRTs7Ozs7NEJBa0RsQjs7Ozs7O3dCQUlEM0IsMEJBQ0MsOERBQUMyRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDc0I7a0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9kLDhEQUFDdkI7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSTs0QkFBRUosV0FBVTtzQ0FDVk8sbUJBQW1CLE1BQ2hCLHlEQUNBQSxrQkFBa0IsS0FDbEIsc0NBQ0FBLGtCQUFrQixLQUNsQiwyQ0FDQTs7Ozs7O3NDQUdOLDhEQUFDSDs0QkFBRUosV0FBVTtzQ0FBZTs7Ozs7O3NDQUc1Qiw4REFBQ0k7NEJBQUVKLFdBQVU7O2dDQUFlOzhDQUNULDhEQUFDc0I7b0NBQUt0QixXQUFVOzhDQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQUtwRXRELGtDQUNDLDhEQUFDaEIsdURBQVlBO29CQUFDSSxRQUFRQTtvQkFBUXVHLFNBQVNwRDs7Ozs7O2dCQUl4Q25DLDhCQUNDLDhEQUFDbkIseURBQWFBO29CQUFDZ0csTUFBTTdFO29CQUFjdUYsU0FBUzlDOzs7Ozs7Z0JBSTdDdkMsZ0JBQWdCVixzQkFDZiw4REFBQ1YseURBQWFBO29CQUNaMEcsVUFBVWhHLEtBQUtxRSxJQUFJO29CQUNuQjRCLFVBQVVyRDtvQkFDVnNELFFBQVFwRDs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNcEI7R0F0WWdCdkQ7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXHNyY1xcY29tcG9uZW50c1xcUm91dGluZVRyYWNrZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIERhaWx5UHJvZ3Jlc3MsXG4gIGdldEN1cnJlbnREYXRlLFxuICBnZXRDdXJyZW50VGltZXN0YW1wLFxuICBjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZSxcbiAgZ2V0VG9kYXlSb3V0aW5lSXRlbXMsXG4gIGdldE5pZ2VyaWFuVGltZURpc3BsYXksXG4gIFJvdXRpbmVJdGVtXG59IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHtcbiAgZ2V0VG9kYXlQcm9ncmVzcyxcbiAgc2F2ZURhaWx5UHJvZ3Jlc3MsXG4gIGNoZWNrQW5kUGVyZm9ybURhaWx5UmVzZXQsXG4gIHNldEN1cnJlbnRVc2VyLFxuICB1c2VySGFzUGluLFxuICBzZXRQaW5Gb3JVc2VyXG59IGZyb20gJ0AvdXRpbHMvc3RvcmFnZSc7XG5pbXBvcnQgeyBXZWVrbHlSZXBvcnQgfSBmcm9tICcuL1dlZWtseVJlcG9ydCc7XG5pbXBvcnQgeyBUYXNrSW5mb1BvcHVwIH0gZnJvbSAnLi9UYXNrSW5mb1BvcHVwJztcbmltcG9ydCB7IFBpblNldHVwUG9wdXAgfSBmcm9tICcuL1BpblNldHVwUG9wdXAnO1xuXG5pbnRlcmZhY2UgUm91dGluZVRyYWNrZXJQcm9wcyB7XG4gIHVzZXJJZDogc3RyaW5nO1xuICBvbkxvZ291dDogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFJvdXRpbmVUcmFja2VyKHsgdXNlcklkLCBvbkxvZ291dCB9OiBSb3V0aW5lVHJhY2tlclByb3BzKSB7XG4gIGNvbnN0IFtwcm9ncmVzcywgc2V0UHJvZ3Jlc3NdID0gdXNlU3RhdGU8RGFpbHlQcm9ncmVzcyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtpc1NhdmluZywgc2V0SXNTYXZpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTx7IGlkOiBzdHJpbmc7IG5hbWU6IHN0cmluZyB9IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaG93TWVudSwgc2V0U2hvd01lbnVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1dlZWtseVJlcG9ydCwgc2V0U2hvd1dlZWtseVJlcG9ydF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt0b2RheVJvdXRpbmVJdGVtcywgc2V0VG9kYXlSb3V0aW5lSXRlbXNdID0gdXNlU3RhdGUoZ2V0VG9kYXlSb3V0aW5lSXRlbXMoKSk7XG4gIGNvbnN0IFtzZWxlY3RlZFRhc2ssIHNldFNlbGVjdGVkVGFza10gPSB1c2VTdGF0ZTxSb3V0aW5lSXRlbSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2hvd1BpblNldHVwLCBzZXRTaG93UGluU2V0dXBdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaW5pdGlhbGl6ZVRyYWNrZXIoKTtcbiAgfSwgW3VzZXJJZF0pO1xuXG4gIC8vIFVwZGF0ZSByb3V0aW5lIGl0ZW1zIHdoZW4gZGF5IGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1cGRhdGVSb3V0aW5lSXRlbXMgPSAoKSA9PiB7XG4gICAgICBzZXRUb2RheVJvdXRpbmVJdGVtcyhnZXRUb2RheVJvdXRpbmVJdGVtcygpKTtcbiAgICB9O1xuXG4gICAgLy8gVXBkYXRlIGltbWVkaWF0ZWx5XG4gICAgdXBkYXRlUm91dGluZUl0ZW1zKCk7XG5cbiAgICAvLyBTZXQgdXAgaW50ZXJ2YWwgdG8gY2hlY2sgZm9yIGRheSBjaGFuZ2UgZXZlcnkgbWludXRlXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICB1cGRhdGVSb3V0aW5lSXRlbXMoKTtcbiAgICB9LCA2MDAwMCk7IC8vIENoZWNrIGV2ZXJ5IG1pbnV0ZVxuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaW5pdGlhbGl6ZVRyYWNrZXIgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBmb3IgZGFpbHkgcmVzZXRcbiAgICAgIGNoZWNrQW5kUGVyZm9ybURhaWx5UmVzZXQodXNlcklkKTtcbiAgICAgIFxuICAgICAgLy8gR2V0IHVzZXIgaW5mb1xuICAgICAgY29uc3QgdXNlcnMgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyb3V0aW5lX3RyYWNrZXJfdXNlcnMnKSB8fCAnW10nKTtcbiAgICAgIGNvbnN0IGN1cnJlbnRVc2VyID0gdXNlcnMuZmluZCgodTogeyBpZDogc3RyaW5nOyBuYW1lOiBzdHJpbmcgfSkgPT4gdS5pZCA9PT0gdXNlcklkKTtcbiAgICAgIHNldFVzZXIoY3VycmVudFVzZXIpO1xuICAgICAgXG4gICAgICAvLyBHZXQgdG9kYXkncyBwcm9ncmVzc1xuICAgICAgY29uc3QgdG9kYXlQcm9ncmVzcyA9IGdldFRvZGF5UHJvZ3Jlc3ModXNlcklkKTtcbiAgICAgIFxuICAgICAgaWYgKHRvZGF5UHJvZ3Jlc3MpIHtcbiAgICAgICAgc2V0UHJvZ3Jlc3ModG9kYXlQcm9ncmVzcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBDcmVhdGUgbmV3IHByb2dyZXNzIGZvciB0b2RheVxuICAgICAgICBjb25zdCBuZXdQcm9ncmVzczogRGFpbHlQcm9ncmVzcyA9IHtcbiAgICAgICAgICB1c2VySWQsXG4gICAgICAgICAgZGF0ZTogZ2V0Q3VycmVudERhdGUoKSxcbiAgICAgICAgICBjb21wbGV0ZWRJdGVtczogW10sXG4gICAgICAgICAgbGFzdFVwZGF0ZWQ6IGdldEN1cnJlbnRUaW1lc3RhbXAoKVxuICAgICAgICB9O1xuICAgICAgICBzZXRQcm9ncmVzcyhuZXdQcm9ncmVzcyk7XG4gICAgICAgIHNhdmVEYWlseVByb2dyZXNzKG5ld1Byb2dyZXNzKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBuZWVkcyBQSU4gc2V0dXAgKG9ubHkgc2hvdyBvbmNlKVxuICAgICAgaWYgKCF1c2VySGFzUGluKHVzZXJJZCkpIHtcbiAgICAgICAgLy8gU2hvdyBQSU4gc2V0dXAgcG9wdXAgYWZ0ZXIgYSBzaG9ydCBkZWxheVxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICBzZXRTaG93UGluU2V0dXAodHJ1ZSk7XG4gICAgICAgIH0sIDEwMDApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgdHJhY2tlcjonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHRvZ2dsZUl0ZW0gPSBhc3luYyAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXByb2dyZXNzIHx8IGlzU2F2aW5nKSByZXR1cm47XG5cbiAgICBzZXRJc1NhdmluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXBkYXRlZEl0ZW1zID0gcHJvZ3Jlc3MuY29tcGxldGVkSXRlbXMuaW5jbHVkZXMoaXRlbUlkKVxuICAgICAgICA/IHByb2dyZXNzLmNvbXBsZXRlZEl0ZW1zLmZpbHRlcihpZCA9PiBpZCAhPT0gaXRlbUlkKVxuICAgICAgICA6IFsuLi5wcm9ncmVzcy5jb21wbGV0ZWRJdGVtcywgaXRlbUlkXTtcblxuICAgICAgY29uc3QgdXBkYXRlZFByb2dyZXNzOiBEYWlseVByb2dyZXNzID0ge1xuICAgICAgICAuLi5wcm9ncmVzcyxcbiAgICAgICAgY29tcGxldGVkSXRlbXM6IHVwZGF0ZWRJdGVtcyxcbiAgICAgICAgbGFzdFVwZGF0ZWQ6IGdldEN1cnJlbnRUaW1lc3RhbXAoKVxuICAgICAgfTtcblxuICAgICAgc2V0UHJvZ3Jlc3ModXBkYXRlZFByb2dyZXNzKTtcbiAgICAgIHNhdmVEYWlseVByb2dyZXNzKHVwZGF0ZWRQcm9ncmVzcyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHByb2dyZXNzOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNTYXZpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSAoKSA9PiB7XG4gICAgc2V0Q3VycmVudFVzZXIoJycpO1xuICAgIG9uTG9nb3V0KCk7XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlTWVudSA9ICgpID0+IHtcbiAgICBzZXRTaG93TWVudSghc2hvd01lbnUpO1xuICB9O1xuXG4gIGNvbnN0IG9wZW5XZWVrbHlSZXBvcnQgPSAoKSA9PiB7XG4gICAgc2V0U2hvd1dlZWtseVJlcG9ydCh0cnVlKTtcbiAgICBzZXRTaG93TWVudShmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgY2xvc2VXZWVrbHlSZXBvcnQgPSAoKSA9PiB7XG4gICAgc2V0U2hvd1dlZWtseVJlcG9ydChmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2V0UGluID0gKHBpbjogc3RyaW5nKSA9PiB7XG4gICAgc2V0UGluRm9yVXNlcih1c2VySWQsIHBpbik7XG4gICAgc2V0U2hvd1BpblNldHVwKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTa2lwUGluID0gKCkgPT4ge1xuICAgIHNldFNob3dQaW5TZXR1cChmYWxzZSk7XG4gIH07XG5cbiAgY29uc3Qgb3BlblRhc2tJbmZvID0gKHRhc2s6IFJvdXRpbmVJdGVtKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRUYXNrKHRhc2spO1xuICB9O1xuXG4gIGNvbnN0IGNsb3NlVGFza0luZm8gPSAoKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRUYXNrKG51bGwpO1xuICB9O1xuXG4gIC8vIENsb3NlIG1lbnUgd2hlbiBjbGlja2luZyBvdXRzaWRlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlQ2xpY2tPdXRzaWRlID0gKGV2ZW50OiBNb3VzZUV2ZW50KSA9PiB7XG4gICAgICBpZiAoc2hvd01lbnUpIHtcbiAgICAgICAgY29uc3QgdGFyZ2V0ID0gZXZlbnQudGFyZ2V0IGFzIEVsZW1lbnQ7XG4gICAgICAgIGlmICghdGFyZ2V0LmNsb3Nlc3QoJy5yZWxhdGl2ZScpKSB7XG4gICAgICAgICAgc2V0U2hvd01lbnUoZmFsc2UpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfTtcblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgfTtcbiAgfSwgW3Nob3dNZW51XSk7XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGltZ1xuICAgICAgICAgICAgc3JjPVwiL2ljb24ucG5nXCJcbiAgICAgICAgICAgIGFsdD1cIlJvdXRpbmUgVHJhY2tlclwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byBtYi00IHJvdW5kZWQtbGcgc2hhZG93LW1kXCJcbiAgICAgICAgICAvPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTJcIj5Mb2FkaW5nIHlvdXIgdHJhY2tlci4uLjwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtaW5kaWdvLTYwMCBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICBCdWlsdCB3aXRoIOKdpO+4jyBieSBUZWNoIFRhbGtcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmICghcHJvZ3Jlc3MpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMFwiPkVycm9yIGxvYWRpbmcgeW91ciBwcm9ncmVzcy4gUGxlYXNlIHRyeSBhZ2Fpbi48L3A+XG4gICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9e2luaXRpYWxpemVUcmFja2VyfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNCBweC00IHB5LTIgYmctaW5kaWdvLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctaW5kaWdvLTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgUmV0cnlcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgY29uc3QgY29tcGxldGlvblJhdGUgPSBjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZShwcm9ncmVzcy5jb21wbGV0ZWRJdGVtcywgdG9kYXlSb3V0aW5lSXRlbXMubGVuZ3RoKTtcbiAgY29uc3QgY3VycmVudERhdGUgPSBnZXROaWdlcmlhblRpbWVEaXNwbGF5KCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBwLTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTYgbWItNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgIHNyYz1cIi9pY29uLnBuZ1wiXG4gICAgICAgICAgICAgICAgYWx0PVwiUm91dGluZSBUcmFja2VyXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgcm91bmRlZC1sZyBzaGFkb3ctbWRcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgV2VsY29tZSBiYWNrLCB7dXNlcj8ubmFtZSB8fCAnRnJpZW5kJ30hIPCfkYtcbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57Y3VycmVudERhdGV9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgey8qIE1lbnUgQnV0dG9uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTWVudX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIk1lbnVcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy02IGgtNlwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9XCIyXCIgZD1cIk00IDZoMTZNNCAxMmgxNk00IDE4aDE2XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIERyb3Bkb3duIE1lbnUgKi99XG4gICAgICAgICAgICAgICAge3Nob3dNZW51ICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCBtdC0yIHctNDggYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBweS0yIHotMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5XZWVrbHlSZXBvcnR9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC00IHB5LTIgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+8J+Tijwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5XZWVrbHkgUmVwb3J0PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0xMDAgbXktMVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0yIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPvCfkaQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+U3dpdGNoIFVzZXI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICB7LyogUHJvZ3Jlc3MgU3VtbWFyeSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1pbmRpZ28tNTAgdG8tYmx1ZS01MCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5Ub2RheXMgUHJvZ3Jlc3M8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtaW5kaWdvLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge3Byb2dyZXNzLmNvbXBsZXRlZEl0ZW1zLmxlbmd0aH0ve3RvZGF5Um91dGluZUl0ZW1zLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5Db21wbGV0aW9uIFJhdGU8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtaW5kaWdvLTYwMFwiPntjb21wbGV0aW9uUmF0ZX0lPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogUHJvZ3Jlc3MgQmFyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1pbmRpZ28tNjAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtjb21wbGV0aW9uUmF0ZX0lYCB9fVxuICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJvdXRpbmUgSXRlbXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC02XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTZcIj5cbiAgICAgICAgICAgIERhaWx5IFJvdXRpbmUgQ2hlY2tsaXN0IOKchVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgIHt0b2RheVJvdXRpbmVJdGVtcy5tYXAoKGl0ZW0pID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgaXNDb21wbGV0ZWQgPSBwcm9ncmVzcy5jb21wbGV0ZWRJdGVtcy5pbmNsdWRlcyhpdGVtLmlkKTtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLmlkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcC00IHJvdW5kZWQtbGcgYm9yZGVyLTIgdHJhbnNpdGlvbi1hbGwgY3Vyc29yLXBvaW50ZXIgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNDb21wbGV0ZWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JlZW4tMjAwIGJnLWdyZWVuLTUwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBiZy1ncmF5LTUwIGhvdmVyOmJvcmRlci1pbmRpZ28tMjAwIGhvdmVyOmJnLWluZGlnby01MCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlSXRlbShpdGVtLmlkKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bFwiPntpdGVtLmljb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7aXNDb21wbGV0ZWQgPyAndGV4dC1ncmVlbi04MDAnIDogJ3RleHQtZ3JheS04MDAnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gJHtpc0NvbXBsZXRlZCA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1ncmF5LTYwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBJbmZvIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICAgICAge2l0ZW0uZGV0YWlsZWRJbmZvICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVuVGFza0luZm8oaXRlbSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy02IGgtNiByb3VuZGVkLWZ1bGwgYmctaW5kaWdvLTEwMCBob3ZlcjpiZy1pbmRpZ28tMjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiTW9yZSBpbmZvXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWluZGlnby02MDAgdGV4dC1zbSBmb250LWJvbGRcIj7ik5g8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIENoZWNrYm94ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctNiBoLTYgcm91bmRlZC1mdWxsIGJvcmRlci0yIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyICR7XG4gICAgICAgICAgICAgICAgICAgICAgaXNDb21wbGV0ZWRcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ncmVlbi01MDAgYmctZ3JlZW4tNTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2lzQ29tcGxldGVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xNi43MDcgNS4yOTNhMSAxIDAgMDEwIDEuNDE0bC04IDhhMSAxIDAgMDEtMS40MTQgMGwtNC00YTEgMSAwIDAxMS40MTQtMS40MTRMOCAxMi41ODZsNy4yOTMtNy4yOTNhMSAxIDAgMDExLjQxNCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgey8qIFNhdmUgU3RhdHVzICovfVxuICAgICAgICAgIHtpc1NhdmluZyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgdGV4dC1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby02MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3Bhbj5TYXZpbmcuLi48L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1vdGl2YXRpb25hbCBGb290ZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtNiB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAge2NvbXBsZXRpb25SYXRlID09PSAxMDBcbiAgICAgICAgICAgICAgPyBcIvCfjokgQW1hemluZyEgWW91dmUgY29tcGxldGVkIGFsbCB5b3VyIHJvdXRpbmVzIHRvZGF5IVwiXG4gICAgICAgICAgICAgIDogY29tcGxldGlvblJhdGUgPj0gNzVcbiAgICAgICAgICAgICAgPyBcIvCflKUgWW91cmUgZG9pbmcgZ3JlYXQhIEtlZXAgaXQgdXAhXCJcbiAgICAgICAgICAgICAgOiBjb21wbGV0aW9uUmF0ZSA+PSA1MFxuICAgICAgICAgICAgICA/IFwi8J+SqiBHb29kIHByb2dyZXNzISBZb3VyZSBoYWxmd2F5IHRoZXJlIVwiXG4gICAgICAgICAgICAgIDogXCLwn4yxIEV2ZXJ5IHN0ZXAgY291bnRzLiBZb3V2ZSBnb3QgdGhpcyFcIlxuICAgICAgICAgICAgfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIG10LTJcIj5cbiAgICAgICAgICAgIFByb2dyZXNzIGF1dG8tc2F2ZXMg4oCiIFJlc2V0cyBhdCBtaWRuaWdodCDigKIgQWNjZXNzIFdlZWtseSBSZXBvcnQgZnJvbSBtZW51XG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgbXQtMlwiPlxuICAgICAgICAgICAgQnVpbHQgd2l0aCDinaTvuI8gYnkgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWluZGlnby02MDBcIj5UZWNoIFRhbGs8L3NwYW4+XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogV2Vla2x5IFJlcG9ydCBNb2RhbCAqL31cbiAgICAgICAge3Nob3dXZWVrbHlSZXBvcnQgJiYgKFxuICAgICAgICAgIDxXZWVrbHlSZXBvcnQgdXNlcklkPXt1c2VySWR9IG9uQ2xvc2U9e2Nsb3NlV2Vla2x5UmVwb3J0fSAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBUYXNrIEluZm8gUG9wdXAgKi99XG4gICAgICAgIHtzZWxlY3RlZFRhc2sgJiYgKFxuICAgICAgICAgIDxUYXNrSW5mb1BvcHVwIGl0ZW09e3NlbGVjdGVkVGFza30gb25DbG9zZT17Y2xvc2VUYXNrSW5mb30gLz5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogUElOIFNldHVwIFBvcHVwICovfVxuICAgICAgICB7c2hvd1BpblNldHVwICYmIHVzZXIgJiYgKFxuICAgICAgICAgIDxQaW5TZXR1cFBvcHVwXG4gICAgICAgICAgICB1c2VyTmFtZT17dXNlci5uYW1lfVxuICAgICAgICAgICAgb25TZXRQaW49e2hhbmRsZVNldFBpbn1cbiAgICAgICAgICAgIG9uU2tpcD17aGFuZGxlU2tpcFBpbn1cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJnZXRDdXJyZW50RGF0ZSIsImdldEN1cnJlbnRUaW1lc3RhbXAiLCJjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZSIsImdldFRvZGF5Um91dGluZUl0ZW1zIiwiZ2V0TmlnZXJpYW5UaW1lRGlzcGxheSIsImdldFRvZGF5UHJvZ3Jlc3MiLCJzYXZlRGFpbHlQcm9ncmVzcyIsImNoZWNrQW5kUGVyZm9ybURhaWx5UmVzZXQiLCJzZXRDdXJyZW50VXNlciIsInVzZXJIYXNQaW4iLCJzZXRQaW5Gb3JVc2VyIiwiV2Vla2x5UmVwb3J0IiwiVGFza0luZm9Qb3B1cCIsIlBpblNldHVwUG9wdXAiLCJSb3V0aW5lVHJhY2tlciIsInVzZXJJZCIsIm9uTG9nb3V0IiwicHJvZ3Jlc3MiLCJzZXRQcm9ncmVzcyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImlzU2F2aW5nIiwic2V0SXNTYXZpbmciLCJ1c2VyIiwic2V0VXNlciIsInNob3dNZW51Iiwic2V0U2hvd01lbnUiLCJzaG93V2Vla2x5UmVwb3J0Iiwic2V0U2hvd1dlZWtseVJlcG9ydCIsInRvZGF5Um91dGluZUl0ZW1zIiwic2V0VG9kYXlSb3V0aW5lSXRlbXMiLCJzZWxlY3RlZFRhc2siLCJzZXRTZWxlY3RlZFRhc2siLCJzaG93UGluU2V0dXAiLCJzZXRTaG93UGluU2V0dXAiLCJpbml0aWFsaXplVHJhY2tlciIsInVwZGF0ZVJvdXRpbmVJdGVtcyIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwidXNlcnMiLCJKU09OIiwicGFyc2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiY3VycmVudFVzZXIiLCJmaW5kIiwidSIsImlkIiwidG9kYXlQcm9ncmVzcyIsIm5ld1Byb2dyZXNzIiwiZGF0ZSIsImNvbXBsZXRlZEl0ZW1zIiwibGFzdFVwZGF0ZWQiLCJzZXRUaW1lb3V0IiwiZXJyb3IiLCJjb25zb2xlIiwidG9nZ2xlSXRlbSIsIml0ZW1JZCIsInVwZGF0ZWRJdGVtcyIsImluY2x1ZGVzIiwiZmlsdGVyIiwidXBkYXRlZFByb2dyZXNzIiwiaGFuZGxlTG9nb3V0IiwidG9nZ2xlTWVudSIsIm9wZW5XZWVrbHlSZXBvcnQiLCJjbG9zZVdlZWtseVJlcG9ydCIsImhhbmRsZVNldFBpbiIsInBpbiIsImhhbmRsZVNraXBQaW4iLCJvcGVuVGFza0luZm8iLCJ0YXNrIiwiY2xvc2VUYXNrSW5mbyIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwidGFyZ2V0IiwiY2xvc2VzdCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJpbWciLCJzcmMiLCJhbHQiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImNvbXBsZXRpb25SYXRlIiwibGVuZ3RoIiwiY3VycmVudERhdGUiLCJoMSIsIm5hbWUiLCJ0aXRsZSIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsInNwYW4iLCJzdHlsZSIsIndpZHRoIiwiaDIiLCJtYXAiLCJpdGVtIiwiaXNDb21wbGV0ZWQiLCJpY29uIiwiaDMiLCJkZXNjcmlwdGlvbiIsImRldGFpbGVkSW5mbyIsImUiLCJzdG9wUHJvcGFnYXRpb24iLCJmaWxsUnVsZSIsImNsaXBSdWxlIiwib25DbG9zZSIsInVzZXJOYW1lIiwib25TZXRQaW4iLCJvblNraXAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RoutineTracker.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TaskInfoPopup.tsx":
/*!******************************************!*\
  !*** ./src/components/TaskInfoPopup.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskInfoPopup: () => (/* binding */ TaskInfoPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ TaskInfoPopup auto */ \nvar _s = $RefreshSig$();\n\nfunction TaskInfoPopup(param) {\n    let { item, onClose } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Animation effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TaskInfoPopup.useEffect\": ()=>{\n            setIsVisible(true);\n            // Close when clicking outside\n            const handleClickOutside = {\n                \"TaskInfoPopup.useEffect.handleClickOutside\": (event)=>{\n                    if (popupRef.current && !popupRef.current.contains(event.target)) {\n                        handleClose();\n                    }\n                }\n            }[\"TaskInfoPopup.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"TaskInfoPopup.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"TaskInfoPopup.useEffect\"];\n        }\n    }[\"TaskInfoPopup.useEffect\"], []);\n    // Handle escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TaskInfoPopup.useEffect\": ()=>{\n            const handleEscKey = {\n                \"TaskInfoPopup.useEffect.handleEscKey\": (event)=>{\n                    if (event.key === 'Escape') {\n                        handleClose();\n                    }\n                }\n            }[\"TaskInfoPopup.useEffect.handleEscKey\"];\n            document.addEventListener('keydown', handleEscKey);\n            return ({\n                \"TaskInfoPopup.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscKey);\n                }\n            })[\"TaskInfoPopup.useEffect\"];\n        }\n    }[\"TaskInfoPopup.useEffect\"], []);\n    const handleClose = ()=>{\n        setIsVisible(false);\n        setTimeout(()=>{\n            onClose();\n        }, 300); // Match transition duration\n    };\n    if (!item.detailedInfo) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300\",\n        style: {\n            opacity: isVisible ? 1 : 0\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: popupRef,\n            className: \"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 \".concat(isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl\",\n                                    children: item.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[70vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                    children: \"Purpose\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: item.detailedInfo.purpose\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                    children: \"Benefits\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc pl-5 space-y-1\",\n                                    children: item.detailedInfo.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-gray-600\",\n                                            children: benefit\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                    children: \"Tips\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc pl-5 space-y-1\",\n                                    children: item.detailedInfo.tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-gray-600\",\n                                            children: tip\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-indigo-50 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    item.detailedInfo.timeRecommendation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-indigo-800 mb-1\",\n                                                children: \"Recommended Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-indigo-700\",\n                                                children: item.detailedInfo.timeRecommendation\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.detailedInfo.frequency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-indigo-800 mb-1\",\n                                                children: \"Frequency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-indigo-700\",\n                                                children: item.detailedInfo.frequency\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 p-4 bg-gray-50 rounded-b-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClose,\n                        className: \"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors\",\n                        children: \"Got it\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\TaskInfoPopup.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(TaskInfoPopup, \"WDGsDgb+GifvAoL3/znO6xzs6w0=\");\n_c = TaskInfoPopup;\nvar _c;\n$RefreshReg$(_c, \"TaskInfoPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TaskInfoPopup.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WeeklyReport.tsx":
/*!*****************************************!*\
  !*** ./src/components/WeeklyReport.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeeklyReport: () => (/* binding */ WeeklyReport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ WeeklyReport auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WeeklyReport(param) {\n    let { userId, onClose } = param;\n    _s();\n    const [weeklyData, setWeeklyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0 = current week, 1 = last week, etc.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklyReport.useEffect\": ()=>{\n            loadWeeklyData();\n        }\n    }[\"WeeklyReport.useEffect\"], [\n        userId,\n        selectedWeek\n    ]);\n    const loadWeeklyData = ()=>{\n        setIsLoading(true);\n        const data = [];\n        // Get the start of the selected week (Sunday)\n        const today = new Date();\n        const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - currentDay - selectedWeek * 7);\n        // Generate 7 days of data\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startOfWeek);\n            date.setDate(startOfWeek.getDate() + i);\n            const dateStr = date.toISOString().split('T')[0];\n            const progress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserProgress)(userId, dateStr);\n            const completionRate = progress ? (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.length) : 0;\n            data.push({\n                date: dateStr,\n                dayName: date.toLocaleDateString('en-US', {\n                    weekday: 'short'\n                }),\n                progress,\n                completionRate\n            });\n        }\n        setWeeklyData(data);\n        setIsLoading(false);\n    };\n    const getWeekTitle = ()=>{\n        if (selectedWeek === 0) return 'This Week';\n        if (selectedWeek === 1) return 'Last Week';\n        return \"\".concat(selectedWeek + 1, \" Weeks Ago\");\n    };\n    const getWeeklyAverage = ()=>{\n        const totalRate = weeklyData.reduce((sum, day)=>sum + day.completionRate, 0);\n        return Math.round(totalRate / weeklyData.length);\n    };\n    const getBestDay = ()=>{\n        return weeklyData.reduce((best, day)=>day.completionRate > best.completionRate ? day : best);\n    };\n    const getStreakDays = ()=>{\n        return weeklyData.filter((day)=>day.completionRate > 0).length;\n    };\n    const getTaskStats = ()=>{\n        const taskStats = _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.map((item)=>{\n            const completedDays = weeklyData.filter((day)=>{\n                var _day_progress;\n                return (_day_progress = day.progress) === null || _day_progress === void 0 ? void 0 : _day_progress.completedItems.includes(item.id);\n            }).length;\n            return {\n                ...item,\n                completedDays,\n                percentage: Math.round(completedDays / 7 * 100)\n            };\n        });\n        return taskStats.sort((a, b)=>b.percentage - a.percentage);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading weekly report...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/icon.png\",\n                                    alt: \"Routine Tracker\",\n                                    className: \"w-10 h-10 rounded-lg shadow-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"\\uD83D\\uDCCA Weekly Report\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: getWeekTitle()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedWeek(selectedWeek + 1),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                            title: \"Previous week\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M15 19l-7-7 7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 min-w-[100px] text-center\",\n                                            children: getWeekTitle()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedWeek(Math.max(0, selectedWeek - 1)),\n                                            disabled: selectedWeek === 0,\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            title: \"Next week\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M9 5l7 7-7 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-indigo-50 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-indigo-600\",\n                                            children: [\n                                                getWeeklyAverage(),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-indigo-700\",\n                                            children: \"Weekly Average\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                getStreakDays(),\n                                                \"/7\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-green-700\",\n                                            children: \"Active Days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-50 rounded-lg p-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: [\n                                                getBestDay().completionRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-purple-700\",\n                                            children: [\n                                                \"Best Day (\",\n                                                getBestDay().dayName,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Daily Progress\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-7 gap-2\",\n                                    children: weeklyData.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 mb-2\",\n                                                    children: day.dayName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-200 rounded-lg h-24 flex items-end justify-center p-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full rounded transition-all duration-300 \".concat(day.completionRate >= 80 ? 'bg-green-500' : day.completionRate >= 60 ? 'bg-yellow-500' : day.completionRate >= 40 ? 'bg-orange-500' : day.completionRate > 0 ? 'bg-red-500' : 'bg-gray-300'),\n                                                        style: {\n                                                            height: \"\".concat(Math.max(day.completionRate, 5), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 mt-1\",\n                                                    children: [\n                                                        day.completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"Task Performance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: getTaskStats().map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: task.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: task.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        task.completedDays,\n                                                                        \"/7 days completed\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 rounded-full transition-all duration-300 \".concat(task.percentage >= 80 ? 'bg-green-500' : task.percentage >= 60 ? 'bg-yellow-500' : task.percentage >= 40 ? 'bg-orange-500' : task.percentage > 0 ? 'bg-red-500' : 'bg-gray-300'),\n                                                                style: {\n                                                                    width: \"\".concat(task.percentage, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 w-12 text-right\",\n                                                            children: [\n                                                                task.percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 p-6 bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors mb-3\",\n                            children: \"Close Report\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-xs text-gray-500\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WeeklyReport.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklyReport, \"zgzWMWRWqS+5py8FT5njLdowzik=\");\n_c = WeeklyReport;\nvar _c;\n$RefreshReg$(_c, \"WeeklyReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WeeklyReport.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WelcomeScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/WelcomeScreen.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WelcomeScreen: () => (/* binding */ WelcomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _config_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/auth */ \"(app-pages-browser)/./src/config/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ WelcomeScreen auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WelcomeScreen(param) {\n    let { onUserLogin } = param;\n    _s();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!name.trim()) return;\n        setIsLoading(true);\n        setError('');\n        try {\n            // Check if user is authorized\n            if (!(0,_config_auth__WEBPACK_IMPORTED_MODULE_4__.isUserAuthorized)(name.trim())) {\n                setError('Access denied. You are not authorized to use this tracker.');\n                setIsLoading(false);\n                return;\n            }\n            // Get the display name for authorized user\n            const displayName = (0,_config_auth__WEBPACK_IMPORTED_MODULE_4__.getUserDisplayName)(name.trim());\n            // Check if user already exists (try both input name and display name)\n            let user = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(name.trim()) || (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(displayName);\n            if (!user) {\n                // Create new user with consistent ID based on input name\n                user = {\n                    id: (0,_types__WEBPACK_IMPORTED_MODULE_2__.generateUserId)(name.trim()),\n                    name: displayName,\n                    createdAt: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)(),\n                    lastActive: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            } else {\n                // Update last active time and display name\n                user.lastActive = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)();\n                user.name = displayName; // Update to current display name\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            }\n            // Set as current user\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(user.id);\n            onUserLogin(user.id);\n        } catch (error) {\n            console.error('Error logging in:', error);\n            setError('An error occurred. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/icon.png\",\n                            alt: \"Routine Tracker\",\n                            className: \"w-20 h-20 mx-auto mb-4 rounded-lg shadow-md\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Digital Routine & Results Tracker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Track your daily growth with intention\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-indigo-600 font-semibold mt-2\",\n                            children: \"Built with ❤️ by Tech Talk\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"What were building here \\uD83D\\uDCBB✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Type in your name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tick what youve done for the day (Prayer, Study, Hygiene, Work, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Submit, and it saves your progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Come back anytime before the day ends to update it\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"System resets at midnight, but keeps a history of your growth\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                            children: \"Ready to track your growth? \\uD83D\\uDCCA✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"name\",\n                                            value: name,\n                                            onChange: (e)=>setName(e.target.value),\n                                            placeholder: \"Enter your name...\",\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors\",\n                                            disabled: isLoading,\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !name.trim(),\n                                    className: \"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Getting started...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this) : 'Start Tracking 🔥'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No login stress. No judgment. Just you, your goals, and your growth.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(WelcomeScreen, \"Vpk21IVA7x4GlcvEaBy32vnfD0o=\");\n_c = WelcomeScreen;\nvar _c;\n$RefreshReg$(_c, \"WelcomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WelcomeScreen.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/auth.ts":
/*!****************************!*\
  !*** ./src/config/auth.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZED_USERS: () => (/* binding */ AUTHORIZED_USERS),\n/* harmony export */   getAllAuthorizedNames: () => (/* binding */ getAllAuthorizedNames),\n/* harmony export */   getAuthorizedUser: () => (/* binding */ getAuthorizedUser),\n/* harmony export */   getAuthorizedUsersCount: () => (/* binding */ getAuthorizedUsersCount),\n/* harmony export */   getUserDisplayName: () => (/* binding */ getUserDisplayName),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isUserAuthorized: () => (/* binding */ isUserAuthorized)\n/* harmony export */ });\n// Authorized users configuration for the Digital Routine & Results Tracker\n// Only users with names in this list can access the application\n// List of authorized users - Add names here to grant access\nconst AUTHORIZED_USERS = [\n    {\n        name: \"Obinna\",\n        displayName: \"Mchost\",\n        role: \"admin\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"Daniel\",\n        displayName: \"Aj danny Roze\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    }\n];\n// Utility functions for user authorization\nconst isUserAuthorized = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    return AUTHORIZED_USERS.some((user)=>user.name.toLowerCase() === normalizedInput);\n};\nconst getAuthorizedUser = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    return AUTHORIZED_USERS.find((user)=>user.name.toLowerCase() === normalizedInput) || null;\n};\nconst getUserDisplayName = (inputName)=>{\n    const user = getAuthorizedUser(inputName);\n    return (user === null || user === void 0 ? void 0 : user.displayName) || inputName;\n};\nconst isAdmin = (inputName)=>{\n    const user = getAuthorizedUser(inputName);\n    return (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n};\n// Get total number of authorized users\nconst getAuthorizedUsersCount = ()=>{\n    return AUTHORIZED_USERS.length;\n};\n// Get all authorized user names (for admin purposes)\nconst getAllAuthorizedNames = ()=>{\n    return AUTHORIZED_USERS.map((user)=>user.displayName || user.name);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   getNigerianTimeDisplay: () => (/* binding */ getNigerianTimeDisplay),\n/* harmony export */   getRoutineItemsForDay: () => (/* binding */ getRoutineItemsForDay),\n/* harmony export */   getTodayRoutineItems: () => (/* binding */ getTodayRoutineItems),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection',\n        detailedInfo: {\n            purpose: 'Connect with the divine, find inner peace, and seek guidance for daily life.',\n            benefits: [\n                'Reduces stress and anxiety',\n                'Provides spiritual guidance and clarity',\n                'Strengthens faith and spiritual connection',\n                'Promotes gratitude and mindfulness',\n                'Offers comfort during difficult times'\n            ],\n            tips: [\n                'Set aside a quiet, dedicated space for prayer',\n                'Choose a consistent time each day',\n                'Start with gratitude and thanksgiving',\n                'Include prayers for others, not just yourself',\n                'Listen for guidance and inner peace'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development',\n        detailedInfo: {\n            purpose: 'Expand knowledge, develop skills, and pursue personal or professional growth through dedicated learning.',\n            benefits: [\n                'Improves cognitive function and memory',\n                'Enhances career prospects and opportunities',\n                'Builds confidence and self-esteem',\n                'Keeps mind sharp and engaged',\n                'Opens new perspectives and ideas'\n            ],\n            tips: [\n                'Create a distraction-free study environment',\n                'Use active learning techniques (notes, summaries)',\n                'Take regular breaks to maintain focus',\n                'Set specific, achievable learning goals',\n                'Review and practice regularly for retention'\n            ],\n            timeRecommendation: '30-120 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness',\n        detailedInfo: {\n            purpose: 'Maintain personal cleanliness, health, and confidence through proper hygiene practices.',\n            benefits: [\n                'Prevents illness and infections',\n                'Boosts self-confidence and social acceptance',\n                'Improves overall health and well-being',\n                'Creates positive first impressions',\n                'Reduces stress and anxiety about appearance'\n            ],\n            tips: [\n                'Establish a consistent daily routine',\n                'Use quality hygiene products suited for your skin type',\n                'Pay attention to often-missed areas (behind ears, between toes)',\n                'Replace hygiene items regularly (toothbrush, razors)',\n                'Stay hydrated to support healthy skin and hair'\n            ],\n            timeRecommendation: '20-45 minutes',\n            frequency: 'Daily'\n        }\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities',\n        detailedInfo: {\n            purpose: 'Accomplish professional goals, contribute value, and advance career through focused work.',\n            benefits: [\n                'Provides financial stability and security',\n                'Builds professional skills and experience',\n                'Creates sense of purpose and achievement',\n                'Develops problem-solving abilities',\n                'Expands professional network and opportunities'\n            ],\n            tips: [\n                'Set clear daily and weekly goals',\n                'Prioritize tasks using time management techniques',\n                'Take regular breaks to maintain productivity',\n                'Minimize distractions during focused work time',\n                'Continuously learn and improve your skills'\n            ],\n            timeRecommendation: '6-8 hours',\n            frequency: 'Weekdays'\n        }\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness',\n        detailedInfo: {\n            purpose: 'Maintain physical health, build strength, and improve overall well-being through regular exercise.',\n            benefits: [\n                'Improves cardiovascular health and endurance',\n                'Builds muscle strength and bone density',\n                'Enhances mental health and reduces stress',\n                'Boosts energy levels and sleep quality',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Start with activities you enjoy to build consistency',\n                'Gradually increase intensity and duration',\n                'Include both cardio and strength training',\n                'Stay hydrated before, during, and after exercise',\n                'Listen to your body and allow for rest days'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily or 5-6 times per week'\n        }\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning',\n        detailedInfo: {\n            purpose: 'Fuel your body with nutritious foods to support optimal health, energy, and well-being.',\n            benefits: [\n                'Provides essential nutrients for body functions',\n                'Maintains stable energy levels throughout the day',\n                'Supports immune system and disease prevention',\n                'Improves mental clarity and cognitive function',\n                'Helps maintain healthy weight and metabolism'\n            ],\n            tips: [\n                'Plan meals in advance to avoid unhealthy choices',\n                'Include a variety of colorful fruits and vegetables',\n                'Stay hydrated with plenty of water',\n                'Practice portion control and mindful eating',\n                'Limit processed foods and added sugars'\n            ],\n            timeRecommendation: '30-60 minutes for meal prep',\n            frequency: 'Daily meal planning and preparation'\n        }\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection',\n        detailedInfo: {\n            purpose: 'Gain self-awareness, process experiences, and promote personal growth through thoughtful reflection.',\n            benefits: [\n                'Increases self-awareness and emotional intelligence',\n                'Helps process and learn from experiences',\n                'Reduces stress and promotes mental clarity',\n                'Identifies patterns and areas for improvement',\n                'Enhances gratitude and positive mindset'\n            ],\n            tips: [\n                'Set aside quiet time without distractions',\n                'Write freely without worrying about grammar',\n                'Ask yourself meaningful questions about your day',\n                'Focus on both challenges and achievements',\n                'Review past entries to track growth and patterns'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily, preferably evening'\n        }\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions',\n        detailedInfo: {\n            purpose: 'Build and maintain meaningful relationships that provide support, joy, and personal growth.',\n            benefits: [\n                'Reduces feelings of loneliness and isolation',\n                'Provides emotional support and encouragement',\n                'Enhances mental health and well-being',\n                'Creates opportunities for learning and growth',\n                'Builds a strong support network for life challenges'\n            ],\n            tips: [\n                'Be present and actively listen during conversations',\n                'Reach out to friends and family regularly',\n                'Engage in shared activities and interests',\n                'Show genuine interest in others\\' lives and experiences',\n                'Practice empathy and offer support when needed'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily interactions, deeper connections weekly'\n        }\n    },\n    {\n        id: 'fasting',\n        name: 'Fasting',\n        icon: '🌙',\n        description: 'Spiritual fasting practice',\n        daysOfWeek: [\n            3,\n            5\n        ],\n        detailedInfo: {\n            purpose: 'Engage in spiritual discipline, self-control, and deeper connection with faith through fasting.',\n            benefits: [\n                'Develops self-discipline and willpower',\n                'Enhances spiritual awareness and focus',\n                'Promotes gratitude for daily blessings',\n                'Encourages prayer and meditation',\n                'Builds empathy for those less fortunate'\n            ],\n            tips: [\n                'Start with shorter fasting periods if new to fasting',\n                'Stay hydrated with water throughout the day',\n                'Use fasting time for prayer and reflection',\n                'Break your fast gently with light, healthy foods',\n                'Consult healthcare provider if you have medical conditions'\n            ],\n            timeRecommendation: 'Sunrise to sunset',\n            frequency: 'Wednesdays and Fridays'\n        }\n    }\n];\n// Get Nigerian time (West Africa Time - UTC+1)\nconst getNigerianTime = ()=>{\n    // Create a date in Nigerian timezone\n    const now = new Date();\n    const nigerianTimeString = now.toLocaleString('en-US', {\n        timeZone: 'Africa/Lagos'\n    });\n    return new Date(nigerianTimeString);\n};\n// Get routine items for a specific day (filters by day of week)\nconst getRoutineItemsForDay = (date)=>{\n    const targetDate = date || getNigerianTime();\n    const dayOfWeek = targetDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday\n    return DEFAULT_ROUTINE_ITEMS.filter((item)=>{\n        // If no specific days are defined, show the item every day\n        if (!item.daysOfWeek || item.daysOfWeek.length === 0) {\n            return true;\n        }\n        // Otherwise, only show on specified days\n        return item.daysOfWeek.includes(dayOfWeek);\n    });\n};\n// Get routine items for today\nconst getTodayRoutineItems = ()=>{\n    return getRoutineItemsForDay();\n};\n// Utility functions for date handling (using Nigerian time)\nconst getCurrentDate = ()=>{\n    return getNigerianTime().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return getNigerianTime().toISOString();\n};\n// Get current Nigerian time for display\nconst getNigerianTimeDisplay = ()=>{\n    return new Date().toLocaleDateString('en-US', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        timeZone: 'Africa/Lagos'\n    });\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (consistent based on name only)\nconst generateUserId = (name)=>{\n    // Create a consistent ID based on the name only (no timestamp)\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    // Add a simple hash to make it more unique but still consistent\n    let hash = 0;\n    for(let i = 0; i < name.length; i++){\n        const char = name.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return \"\".concat(nameHash, \"-\").concat(Math.abs(hash));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/storage.ts":
/*!******************************!*\
  !*** ./src/utils/storage.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStreak: () => (/* binding */ calculateStreak),\n/* harmony export */   checkAndPerformDailyReset: () => (/* binding */ checkAndPerformDailyReset),\n/* harmony export */   clearAllData: () => (/* binding */ clearAllData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getDailyProgress: () => (/* binding */ getDailyProgress),\n/* harmony export */   getHistoricalData: () => (/* binding */ getHistoricalData),\n/* harmony export */   getTodayProgress: () => (/* binding */ getTodayProgress),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserHistoricalData: () => (/* binding */ getUserHistoricalData),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   hashPin: () => (/* binding */ hashPin),\n/* harmony export */   saveDailyProgress: () => (/* binding */ saveDailyProgress),\n/* harmony export */   saveHistoricalData: () => (/* binding */ saveHistoricalData),\n/* harmony export */   saveUser: () => (/* binding */ saveUser),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setPinForUser: () => (/* binding */ setPinForUser),\n/* harmony export */   userHasPin: () => (/* binding */ userHasPin),\n/* harmony export */   verifyPin: () => (/* binding */ verifyPin)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n// Local storage utilities for the Digital Routine & Results Tracker\n\nconst STORAGE_KEYS = {\n    USERS: 'routine_tracker_users',\n    DAILY_PROGRESS: 'routine_tracker_daily_progress',\n    HISTORICAL_DATA: 'routine_tracker_historical_data',\n    CURRENT_USER: 'routine_tracker_current_user'\n};\n// User management\nconst saveUser = (user)=>{\n    if (false) {}\n    const users = getUsers();\n    const existingIndex = users.findIndex((u)=>u.id === user.id);\n    if (existingIndex >= 0) {\n        users[existingIndex] = user;\n    } else {\n        users.push(user);\n    }\n    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n};\nconst getUsers = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.USERS);\n    return stored ? JSON.parse(stored) : [];\n};\nconst getUserByName = (name)=>{\n    const users = getUsers();\n    return users.find((u)=>u.name.toLowerCase() === name.toLowerCase()) || null;\n};\nconst setCurrentUser = (userId)=>{\n    if (false) {}\n    localStorage.setItem(STORAGE_KEYS.CURRENT_USER, userId);\n};\nconst getCurrentUser = ()=>{\n    if (false) {}\n    return localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n};\n// PIN management functions\nconst hashPin = (pin)=>{\n    // Simple hash function for PIN (in production, use a proper hashing library)\n    let hash = 0;\n    for(let i = 0; i < pin.length; i++){\n        const char = pin.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString();\n};\nconst setPinForUser = (userId, pin)=>{\n    const users = getUsers();\n    const userIndex = users.findIndex((u)=>u.id === userId);\n    if (userIndex !== -1) {\n        users[userIndex].pin = hashPin(pin);\n        users[userIndex].hasPinSetup = true;\n        saveUsers(users);\n    }\n};\nconst verifyPin = (userId, pin)=>{\n    const users = getUsers();\n    const user = users.find((u)=>u.id === userId);\n    if (!user || !user.pin) return false;\n    return user.pin === hashPin(pin);\n};\nconst userHasPin = (userId)=>{\n    const users = getUsers();\n    const user = users.find((u)=>u.id === userId);\n    return !!(user && user.hasPinSetup);\n};\n// Daily progress management\nconst saveDailyProgress = (progress)=>{\n    if (false) {}\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(progress.userId, \"_\").concat(progress.date);\n    allProgress[key] = progress;\n    localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n};\nconst getDailyProgress = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getTodayProgress = (userId)=>{\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(userId, \"_\").concat((0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)());\n    return allProgress[key] || null;\n};\nconst getUserProgress = (userId, date)=>{\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(userId, \"_\").concat(date);\n    return allProgress[key] || null;\n};\n// Historical data management\nconst saveHistoricalData = (data)=>{\n    if (false) {}\n    const allHistorical = getHistoricalData();\n    const key = \"\".concat(data.userId, \"_\").concat(data.date);\n    allHistorical[key] = data;\n    localStorage.setItem(STORAGE_KEYS.HISTORICAL_DATA, JSON.stringify(allHistorical));\n};\nconst getHistoricalData = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.HISTORICAL_DATA);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getUserHistoricalData = function(userId) {\n    let days = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30;\n    const allHistorical = getHistoricalData();\n    const userHistorical = [];\n    // Get last N days of data\n    for(let i = 0; i < days; i++){\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().split('T')[0];\n        const key = \"\".concat(userId, \"_\").concat(dateStr);\n        if (allHistorical[key]) {\n            userHistorical.push(allHistorical[key]);\n        }\n    }\n    return userHistorical.sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());\n};\n// Daily reset logic\nconst checkAndPerformDailyReset = (userId)=>{\n    const todayProgress = getTodayProgress(userId);\n    // If no progress for today, check if we need to archive yesterday's data\n    if (!todayProgress) {\n        const yesterday = new Date();\n        yesterday.setDate(yesterday.getDate() - 1);\n        const yesterdayStr = yesterday.toISOString().split('T')[0];\n        const yesterdayProgress = getUserProgress(userId, yesterdayStr);\n        if (yesterdayProgress) {\n            // Archive yesterday's progress to historical data\n            const historicalData = {\n                userId: yesterdayProgress.userId,\n                date: yesterdayProgress.date,\n                completedItems: yesterdayProgress.completedItems,\n                completionRate: Math.round(yesterdayProgress.completedItems.length / 8 * 100),\n                streak: calculateStreak(userId, yesterdayStr)\n            };\n            saveHistoricalData(historicalData);\n        }\n        return true; // New day, reset needed\n    }\n    return false; // Same day, no reset needed\n};\n// Calculate current streak\nconst calculateStreak = (userId, endDate)=>{\n    let streak = 0;\n    const date = new Date(endDate);\n    while(true){\n        const dateStr = date.toISOString().split('T')[0];\n        const progress = getUserProgress(userId, dateStr);\n        if (progress && progress.completedItems.length > 0) {\n            streak++;\n            date.setDate(date.getDate() - 1);\n        } else {\n            break;\n        }\n    }\n    return streak;\n};\n// Clear all data (for testing/reset)\nconst clearAllData = ()=>{\n    if (false) {}\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        localStorage.removeItem(key);\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/storage.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);